# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependency directories
node_modules
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
.pnp.*

# Build outputs
dist
dist-ssr
*.local

# Coverage directory used by tools like istanbul
coverage
*.lcov

# Testing
/cypress/videos/
/cypress/screenshots/
/cypress/downloads/
.vitest/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/launch.json
!.vscode/settings.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# Vite
.vite-ssg-dist
.vite-ssg-temp

# Generated types
*.d.ts
!src/shims-vue.d.ts
!src/**/*.d.ts

# Cache files
.eslintcache
.stylelintcache

# Project specific
.windsurfrules

# Editor
.vscode
extensions.json
.cursor

# Yarn
.yarn

# Roo
.roo
.roomodes

# Todo
todo.md

# Claude Code
CLAUDE.md
.claude/*

# Generated files
src/data/generated/