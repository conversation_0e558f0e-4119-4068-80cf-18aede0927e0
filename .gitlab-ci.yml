image: node:18-alpine

stages:
  - build
  - deploy

# 全局变量定义
variables:
  YARN_CACHE_FOLDER: .yarn-cache
  NODE_OPTIONS: '--max-old-space-size=2048'

build:
  stage: build
  cache:
    - key:
        files:
          - yarn.lock
      paths:
        - .yarn-cache/
        - node_modules/
        - .yarn/
    - key: 'vite-cache'
      paths:
        - node_modules/.vite/
  before_script:
    # 安装必要的系统工具
    - apk add --no-cache git
    # 启用 Corepack 并使用项目指定的 Yarn 版本
    - corepack enable
    - corepack prepare yarn@4.1.1 --activate
    # 配置 Yarn 4.x
    - yarn config set httpTimeout 300000
    - yarn config set networkConcurrency 16
    - yarn config set enableGlobalCache false
    - yarn config set cacheFolder .yarn-cache
    # 优化的依赖安装
    - yarn install --immutable || yarn install
  script:
    # 构建项目 - 使用 CI 优化配置
    - NODE_ENV=production yarn build:ci
    # 创建 .htaccess 文件
    - echo 'AddType application/javascript .js' > dist/.htaccess
    - echo 'AddType text/css .css' >> dist/.htaccess
    - echo 'AddType text/html .html' >> dist/.htaccess
    - echo 'AddType image/svg+xml .svg' >> dist/.htaccess
  artifacts:
    paths:
      - dist
    exclude:
      - dist/**/*.map
      - dist/**/*.txt
      - dist/*.woff
      - dist/*.woff2
      - dist/*.ttf
    expire_in: 1 week

pages:
  stage: deploy
  dependencies:
    - build
  script:
    - mkdir -p public
    - cp -r dist/* public/
    - echo '/* /index.html 200' > public/_redirects
  artifacts:
    paths:
      - public
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: always
    - when: never
