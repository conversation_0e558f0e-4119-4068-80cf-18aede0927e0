# Dependencies
node_modules/
.yarn/
.pnp.*

# Build outputs
dist/
dist-ssr/
coverage/
*.local

# Cache and temp files
.nuxt/
.cache/
.temp/
.tmp/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# IDE
.idea/
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Environment files
.env
.env.*

# Specific files
*.min.js
*.min.css
public/*.webm
CHANGELOG.md

# Generated files
*.generated.*
components.d.ts