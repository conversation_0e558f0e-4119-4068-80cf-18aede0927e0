<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <!-- 主渐变 - 加强对比度 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fafafa;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0fdf4;stop-opacity:1" />
    </linearGradient>
    
    <!-- 文字渐变 -->
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    
    <!-- 装饰元素渐变 - 降低透明度避免干扰文字 -->
    <radialGradient id="orbGradient1">
      <stop offset="0%" style="stop-color:#10b98120;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f610;stop-opacity:1" />
    </radialGradient>
    
    <radialGradient id="orbGradient2">
      <stop offset="0%" style="stop-color:#3b82f620;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10b98110;stop-opacity:1" />
    </radialGradient>
    
    <!-- 模糊滤镜 - 增加模糊度减少干扰 -->
    <filter id="blur">
      <feGaussianBlur in="SourceGraphic" stdDeviation="60" />
    </filter>
    
    <!-- 网格图案 -->
    <pattern id="gridPattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
      <line x1="0" y1="0" x2="0" y2="40" stroke="#00000005" stroke-width="1"/>
      <line x1="0" y1="0" x2="40" y2="0" stroke="#00000005" stroke-width="1"/>
    </pattern>
  </defs>
  
  <!-- 背景 -->
  <rect width="1200" height="630" fill="url(#bgGradient)"/>
  
  <!-- 网格图案 -->
  <rect width="1200" height="630" fill="url(#gridPattern)"/>
  
  <!-- 装饰性圆形元素 - 降低不透明度 -->
  <circle cx="150" cy="150" r="200" fill="url(#orbGradient1)" filter="url(#blur)" opacity="0.4"/>
  <circle cx="1050" cy="480" r="250" fill="url(#orbGradient2)" filter="url(#blur)" opacity="0.4"/>
  <circle cx="600" cy="315" r="300" fill="url(#orbGradient1)" filter="url(#blur)" opacity="0.2"/>
  
  <!-- 主标题 -->
  <text x="600" y="200" font-family="system-ui, -apple-system, sans-serif" font-size="80" font-weight="900" text-anchor="middle" fill="#1f2937">
    Breaking Down The
  </text>
  
  <!-- 强调标题 -->
  <text x="600" y="290" font-family="system-ui, -apple-system, sans-serif" font-size="90" font-weight="900" text-anchor="middle" fill="url(#textGradient)">
    Unbreakable
  </text>
  
  <!-- 分隔线 -->
  <rect x="400" y="340" width="400" height="2" fill="#10b98130" rx="1"/>
  
  <!-- 副标题 -->
  <text x="600" y="410" font-family="system-ui, -apple-system, sans-serif" font-size="24" font-weight="300" text-anchor="middle" fill="#4b5563">
    Innovative biological solutions powered by synthetic biology
  </text>
  <text x="600" y="440" font-family="system-ui, -apple-system, sans-serif" font-size="24" font-weight="300" text-anchor="middle" fill="#4b5563">
    to remediate PFAS contamination
  </text>
  
  <!-- 团队和竞赛信息 -->
  <g transform="translate(600, 520)">
    <!-- iGEM 2025 标签 - 增大背景确保文字不超出 -->
    <rect x="-115" y="-20" width="230" height="40" fill="#10b98120" rx="20" stroke="#10b981" stroke-width="1.5"/>
    <text x="0" y="5" font-family="system-ui, -apple-system, sans-serif" font-size="16" font-weight="600" text-anchor="middle" fill="#059669">
      iGEM 2025 • BASIS China
    </text>
  </g>
  
</svg>
