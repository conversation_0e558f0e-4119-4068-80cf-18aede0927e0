import { writeFileSync } from 'fs'
import { join } from 'path'

const baseUrl = 'https://2025.igem.wiki/basis-china'

interface SitemapUrl {
  loc: string
  lastmod: string
  changefreq: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
  priority: number
}

// Define routes manually to avoid importing Vue files
const routes = [
  { path: '/', name: 'home', priority: 1.0, changefreq: 'weekly' },
  { path: '/teammembers', name: 'team-members', priority: 0.8, changefreq: 'monthly' },
  { path: '/attribution', name: 'attribution', priority: 0.7, changefreq: 'monthly' },
  { path: '/human-practices', name: 'human-practices', priority: 0.8, changefreq: 'monthly' },
  { path: '/project', name: 'project', priority: 0.9, changefreq: 'monthly' },
  { path: '/project/overview', name: 'project-overview', priority: 0.9, changefreq: 'monthly' },
  { path: '/project/design', name: 'project-design', priority: 0.9, changefreq: 'monthly' },
  { path: '/wet-lab', name: 'wet-lab', priority: 0.8, changefreq: 'monthly' },
  { path: '/wet-lab/experiment', name: 'wet-lab-experiment', priority: 0.8, changefreq: 'monthly' },
  { path: '/wet-lab/parts', name: 'wet-lab-parts', priority: 0.8, changefreq: 'monthly' },
  { path: '/wet-lab/results', name: 'wet-lab-results', priority: 0.8, changefreq: 'monthly' },
  { path: '/wet-lab/safety', name: 'wet-lab-safety', priority: 0.8, changefreq: 'monthly' },
  {
    path: '/wet-lab/engineering',
    name: 'wet-lab-engineering',
    priority: 0.8,
    changefreq: 'monthly',
  },
  { path: '/dry-lab', name: 'dry-lab', priority: 0.8, changefreq: 'monthly' },
  { path: '/dry-lab/model', name: 'dry-lab-model', priority: 0.8, changefreq: 'monthly' },
  { path: '/dry-lab/hardware', name: 'dry-lab-hardware', priority: 0.8, changefreq: 'monthly' },
  { path: '/dry-lab/software', name: 'dry-lab-software', priority: 0.8, changefreq: 'monthly' },
]

function generateSitemapXml(urls: SitemapUrl[]): string {
  const urlsXml = urls
    .map(
      url => `  <url>
    <loc>${url.loc}</loc>
    <lastmod>${url.lastmod}</lastmod>
    <changefreq>${url.changefreq}</changefreq>
    <priority>${url.priority}</priority>
  </url>`
    )
    .join('\n')

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9
        http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
${urlsXml}
</urlset>`
}

function getRouteUrls(): SitemapUrl[] {
  const today = new Date().toISOString().split('T')[0]

  return routes.map(route => ({
    loc: `${baseUrl}${route.path === '/' ? '' : route.path}`,
    lastmod: today,
    changefreq: (route.changefreq as any) || 'monthly',
    priority: route.priority || 0.8,
  }))
}

// Generate sitemap
const sitemapUrls = getRouteUrls()
const sitemapXml = generateSitemapXml(sitemapUrls)

// Write to public directory
const outputPath = join(process.cwd(), 'public', 'sitemap.xml')
writeFileSync(outputPath, sitemapXml, 'utf-8')

console.log(`✅ Sitemap generated with ${sitemapUrls.length} URLs`)
console.log(`📄 Written to: ${outputPath}`)
