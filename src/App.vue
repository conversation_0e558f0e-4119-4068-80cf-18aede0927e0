<template>
  <div class="flex flex-col min-h-screen bg-transparent">
    <Navbar />
    <div class="flex-grow">
      <PageTransition name="page">
        <router-view />
      </PageTransition>
    </div>
    <Footer />
  </div>
</template>

<script setup lang="ts">
import { watch } from 'vue'
import { useRoute } from 'vue-router'
import Footer from './components/Footer.vue'
import Navbar from './components/Navbar.vue'
import PageTransition from './components/PageTransition.vue'
import { useSEO } from './composables/useSEO'

const route = useRoute()

// Initialize SEO management
useSEO()

// Update title when route changes
watch(
  () => route.meta.title,
  newTitle => {
    if (newTitle && typeof newTitle === 'string') {
      document.title = newTitle
    }
  },
  { immediate: true }
)
</script>

<style>
/* Define fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* CSS Variables for consistent theming */
:root {
  --navbar-height: 64px;

  /* Modern color palette */
  --color-primary-50: #ecfdf5;
  --color-primary-100: #d1fae5;
  --color-primary-200: #a7f3d0;
  --color-primary-300: #6ee7b7;
  --color-primary-400: #34d399;
  --color-primary-500: #10b981;
  --color-primary-600: #059669;
  --color-primary-700: #047857;
  --color-primary-800: #065f46;
  --color-primary-900: #064e3b;

  --color-secondary-50: #eff6ff;
  --color-secondary-100: #dbeafe;
  --color-secondary-200: #bfdbfe;
  --color-secondary-300: #93c5fd;
  --color-secondary-400: #60a5fa;
  --color-secondary-500: #3b82f6;
  --color-secondary-600: #2563eb;
  --color-secondary-700: #1d4ed8;
  --color-secondary-800: #1e40af;
  --color-secondary-900: #1e3a8a;
}

/* Page transitions */
.page-enter-active,
.page-leave-active {
  transition: opacity 0.3s ease;
}

.page-enter-from,
.page-leave-to {
  opacity: 0;
}

/* Global styles */
html {
  scroll-behavior: smooth;
}

body {
  @apply antialiased text-slate-900 bg-white;
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    'Helvetica Neue',
    Arial,
    sans-serif;
}

/* Main content spacing */
.flex-grow {
  padding-top: var(--navbar-height);
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  @apply font-bold tracking-tight;
}

/* Focus styles */
*:focus {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* Selection */
::selection {
  background-color: var(--color-primary-200);
  color: var(--color-primary-900);
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  @apply bg-slate-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-slate-400 rounded-lg;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-slate-500;
}

/* Responsive touch targets */
@media (max-width: 640px) {
  button,
  [role='button'] {
    min-height: 44px;
    min-width: 44px;
  }

  input,
  select,
  textarea {
    font-size: 16px; /* Prevents iOS zoom on focus */
    padding: 0.5rem 0.75rem;
  }
}

/* Utility classes */
.font-title {
  @apply font-black tracking-tight;
}

.text-shadow-sm {
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-shadow-lg {
  text-shadow:
    0 4px 8px rgba(0, 0, 0, 0.12),
    0 2px 4px rgba(0, 0, 0, 0.08);
}

/* Loading states */
.skeleton {
  @apply animate-pulse bg-slate-200 rounded;
}

/* Modern container */
.container-fluid {
  @apply w-full px-4 sm:px-6 lg:px-8;
}

.container {
  @apply container-fluid max-w-7xl mx-auto;
}
</style>
