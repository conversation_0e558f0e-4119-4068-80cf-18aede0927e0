@import '../styles/design-tokens.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* ===================================
     基础排版样式 (Typography)
     =================================== */

  /* 标题样式 */
  .text-h1 {
    font-size: var(--font-size-4xl);
    line-height: var(--line-height-tight);
    font-weight: var(--font-weight-bold);
    letter-spacing: -0.02em;
  }

  .text-h2 {
    font-size: var(--font-size-3xl);
    line-height: var(--line-height-tight);
    font-weight: var(--font-weight-bold);
    letter-spacing: -0.01em;
  }

  .text-h3 {
    font-size: var(--font-size-2xl);
    line-height: var(--line-height-normal);
    font-weight: var(--font-weight-semibold);
  }

  .text-h4 {
    font-size: var(--font-size-xl);
    line-height: var(--line-height-normal);
    font-weight: var(--font-weight-semibold);
  }

  .text-h5 {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-normal);
    font-weight: var(--font-weight-medium);
  }

  /* 正文样式 */
  .text-body {
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
    font-weight: var(--font-weight-normal);
  }

  .text-body-lg {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-relaxed);
    font-weight: var(--font-weight-normal);
  }

  .text-body-sm {
    font-size: var(--font-size-sm);
    line-height: var(--line-height-normal);
    font-weight: var(--font-weight-normal);
  }

  /* 辅助文本样式 */
  .text-caption {
    font-size: var(--font-size-xs);
    line-height: var(--line-height-normal);
    font-weight: var(--font-weight-normal);
    color: var(--color-text-secondary);
  }

  .text-overline {
    font-size: var(--font-size-xs);
    line-height: var(--line-height-normal);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  /* ===================================
     统一的交互状态样式
     =================================== */

  /* 基础交互元素 */
  .interactive {
    transition-property: color, background-color, border-color, transform, box-shadow;
    transition-duration: var(--duration-fast);
    transition-timing-function: var(--ease-in-out);
  }

  /* 悬停状态 */
  .interactive:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  /* 聚焦状态 */
  .interactive:focus:not(:disabled) {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }

  /* 激活状态 */
  .interactive:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
  }

  /* 禁用状态 */
  .interactive:disabled,
  .interactive.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }
}

@layer components {
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* ===================================
     实用工具类 (Utility Classes)
     =================================== */

  /* 间距工具类 */
  .space-1 {
    margin: var(--space-1);
  }
  .space-2 {
    margin: var(--space-2);
  }
  .space-3 {
    margin: var(--space-3);
  }
  .space-4 {
    margin: var(--space-4);
  }
  .space-5 {
    margin: var(--space-5);
  }
  .space-6 {
    margin: var(--space-6);
  }
  .space-7 {
    margin: var(--space-7);
  }
  .space-8 {
    margin: var(--space-8);
  }

  /* 内边距工具类 */
  .pad-1 {
    padding: var(--space-1);
  }
  .pad-2 {
    padding: var(--space-2);
  }
  .pad-3 {
    padding: var(--space-3);
  }
  .pad-4 {
    padding: var(--space-4);
  }
  .pad-5 {
    padding: var(--space-5);
  }
  .pad-6 {
    padding: var(--space-6);
  }
  .pad-7 {
    padding: var(--space-7);
  }
  .pad-8 {
    padding: var(--space-8);
  }

  /* 圆角工具类 */
  .radius-sm {
    border-radius: var(--radius-sm);
  }
  .radius-base {
    border-radius: var(--radius-base);
  }
  .radius-md {
    border-radius: var(--radius-md);
  }
  .radius-lg {
    border-radius: var(--radius-lg);
  }
  .radius-xl {
    border-radius: var(--radius-xl);
  }
  .radius-full {
    border-radius: var(--radius-full);
  }

  /* 阴影工具类 */
  .shadow-sm {
    box-shadow: var(--shadow-sm);
  }
  .shadow-base {
    box-shadow: var(--shadow-base);
  }
  .shadow-md {
    box-shadow: var(--shadow-md);
  }
  .shadow-lg {
    box-shadow: var(--shadow-lg);
  }
  .shadow-xl {
    box-shadow: var(--shadow-xl);
  }

  /* 颜色工具类 */
  .text-primary {
    color: var(--color-text-primary);
  }
  .text-secondary {
    color: var(--color-text-secondary);
  }
  .text-tertiary {
    color: var(--color-text-tertiary);
  }
  .text-disabled {
    color: var(--color-text-disabled);
  }

  .bg-primary {
    background-color: var(--color-primary);
  }
  .bg-secondary {
    background-color: var(--color-secondary);
  }
  .bg-tertiary {
    background-color: var(--color-tertiary);
  }

  /* 边框工具类 */
  .border-default {
    border-color: var(--color-border);
  }
  .border-hover {
    border-color: var(--color-border-hover);
  }
  .border-focus {
    border-color: var(--color-border-focus);
  }
}

/* Custom Animations */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes rotateIn {
  from {
    opacity: 0;
    transform: rotate(-15deg) scale(0.9);
  }
  to {
    opacity: 1;
    transform: rotate(0) scale(1);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 3s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-rotate-in {
  animation: rotateIn 0.5s ease-out forwards;
}

/* 玻璃态效果 - 增强版 */
.glass-effect {
  @apply bg-white/10 backdrop-blur-xl border border-white/20 shadow-lg;
  background-image: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
}

.glass-effect-strong {
  @apply bg-white/20 backdrop-blur-2xl border border-white/30 shadow-xl;
  background-image: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%);
}

.glass-effect-subtle {
  @apply bg-white/5 backdrop-blur-md border border-white/10 shadow-md;
}

/* 高级渐变背景 */
.gradient-bg {
  @apply bg-gradient-to-br from-primary-500 via-tertiary-500 to-secondary-500;
}

.gradient-bg-subtle {
  @apply bg-gradient-to-br from-neutral-50 via-white to-neutral-50;
}

.gradient-bg-mesh {
  background-image: 
    radial-gradient(at 40% 20%, rgba(var(--color-primary-rgb), 0.1) 0px, transparent 50%),
    radial-gradient(at 80% 0%, rgba(var(--color-tertiary-rgb), 0.08) 0px, transparent 50%),
    radial-gradient(at 0% 50%, rgba(var(--color-secondary-rgb), 0.08) 0px, transparent 50%),
    radial-gradient(at 80% 50%, rgba(var(--color-primary-rgb), 0.05) 0px, transparent 50%),
    radial-gradient(at 0% 100%, rgba(var(--color-tertiary-rgb), 0.08) 0px, transparent 50%);
}

.gradient-text {
  @apply bg-gradient-to-r from-primary-600 via-tertiary-600 to-secondary-600 bg-clip-text text-transparent;
}

/* 悬停效果 - 增强版 */
.hover-lift {
  @apply transition-all duration-300 ease-out transform hover:-translate-y-1 hover:shadow-lg;
}

.hover-lift-subtle {
  @apply transition-all duration-300 ease-out transform hover:-translate-y-0.5 hover:shadow-md;
}

.hover-glow {
  @apply transition-all duration-300 hover:shadow-lg hover:shadow-primary-500/20;
}

.hover-scale {
  @apply transition-transform duration-300 ease-out hover:scale-105 active:scale-95;
}

/* 高级阴影效果 */
.shadow-soft {
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.05),
    0 2px 4px -1px rgba(0, 0, 0, 0.03);
}

.shadow-elegant {
  box-shadow: 
    0 10px 25px -5px rgba(0, 0, 0, 0.08),
    0 5px 10px -5px rgba(0, 0, 0, 0.04);
}

.shadow-premium {
  box-shadow: 
    0 20px 40px -15px rgba(0, 0, 0, 0.1),
    0 10px 20px -10px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(0, 0, 0, 0.02);
}

.shadow-colored {
  box-shadow: 
    0 10px 25px -5px rgba(16, 185, 129, 0.15),
    0 5px 10px -5px rgba(16, 185, 129, 0.08);
}

/* 微妙的动画 */
.subtle-bounce {
  animation: subtle-bounce 2s ease-in-out infinite;
}

@keyframes subtle-bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-4px); }
}

.subtle-pulse {
  animation: subtle-pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes subtle-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.9; }
}

/* 高级边框效果 */
.border-gradient {
  @apply relative;
  background-clip: padding-box;
}

.border-gradient::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 2px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-tertiary), var(--color-secondary));
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}

/* 细腻的背景纹理 */
.texture-dots {
  background-image: radial-gradient(circle, rgba(0, 0, 0, 0.02) 1px, transparent 1px);
  background-size: 20px 20px;
}

.texture-grid {
  background-image: 
    linear-gradient(rgba(0, 0, 0, 0.01) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.01) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* 专业的过渡效果 */
.transition-smooth {
  @apply transition-all duration-300 ease-out;
}

.transition-spring {
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  @apply bg-primary-500;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-primary-600;
}
