<template>
  <div class="article-toc-container">
    <!-- Mobile dropdown menu (visible on small screens) -->
    <div
      class="block md:hidden bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 p-4 border border-gray-100"
    >
      <div
        class="flex items-center justify-between cursor-pointer group"
        @click="mobileNavOpen = !mobileNavOpen"
      >
        <h2
          class="text-lg font-semibold flex items-center gap-2 text-gray-900 group-hover:text-primary-700 transition-colors duration-200"
        >
          <div
            class="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center group-hover:bg-primary-200 transition-colors duration-200"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="h-4 w-4 text-primary-600"
            >
              <line x1="3" y1="6" x2="21" y2="6"></line>
              <line x1="3" y1="12" x2="21" y2="12"></line>
              <line x1="3" y1="18" x2="21" y2="18"></line>
            </svg>
          </div>
          {{ title }}
        </h2>
        <div
          class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center group-hover:bg-gray-200 transition-all duration-200"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 transform transition-transform duration-200 text-gray-600"
            :class="{ 'rotate-180': mobileNavOpen }"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clip-rule="evenodd"
            />
          </svg>
        </div>
      </div>
      <transition
        enter-active-class="transition-all duration-200 ease-out"
        enter-from-class="opacity-0 -translate-y-2"
        enter-to-class="opacity-100 translate-y-0"
        leave-active-class="transition-all duration-200 ease-in"
        leave-from-class="opacity-100 translate-y-0"
        leave-to-class="opacity-0 -translate-y-2"
      >
        <div v-if="mobileNavOpen" class="mt-4 border-t border-gray-100 pt-4">
          <ul class="space-y-1">
            <li v-for="(item, index) in items" :key="index">
              <a
                :href="item.url"
                class="block py-2 px-3 text-gray-700 border-l-2 hover:bg-primary-50 hover:text-primary-700 rounded-r transition-all duration-200 group"
                :class="{
                  'border-primary-500 bg-primary-50/50 text-primary-700': isActive(item.url),
                  'border-transparent hover:border-primary-300':
                    !isActive(item.url) && item.depth === 1,
                  'border-transparent hover:border-primary-200':
                    !isActive(item.url) && item.depth !== 1,
                  'font-semibold': item.depth === 1,
                  'text-sm': item.depth > 1,
                }"
                :style="{ paddingLeft: `${Math.max(item.depth - 1, 0) * 1 + 0.75}rem` }"
                @click="handleItemClick"
              >
                <span class="relative">
                  {{ item.title }}
                  <span
                    v-if="isActive(item.url)"
                    class="absolute -left-6 top-1/2 -translate-y-1/2 w-1 h-4 bg-primary-500 rounded-full"
                  ></span>
                </span>
              </a>
            </li>
          </ul>
        </div>
      </transition>
    </div>

    <!-- Desktop sidebar (visible on medium screens and above) -->
    <div
      class="hidden md:block w-full max-w-xs bg-white shadow-sm hover:shadow-md transition-shadow duration-300 rounded-xl border border-gray-100 overflow-hidden"
    >
      <div
        class="p-4 font-medium border-b border-gray-100 bg-gradient-to-r from-primary-50 to-secondary-50 flex items-center gap-3"
      >
        <div class="w-8 h-8 bg-white rounded-lg flex items-center justify-center shadow-sm">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="h-4 w-4 text-primary-600"
          >
            <line x1="3" y1="6" x2="21" y2="6"></line>
            <line x1="3" y1="12" x2="21" y2="12"></line>
            <line x1="3" y1="18" x2="21" y2="18"></line>
          </svg>
        </div>
        <span class="text-gray-900">{{ title }}</span>
      </div>
      <div class="p-4">
        <nav class="space-y-1" role="navigation" aria-label="Table of contents">
          <ul class="space-y-1">
            <li v-for="(item, index) in items" :key="index">
              <a
                :href="item.url"
                class="block py-2 px-3 text-gray-700 border-l-2 hover:bg-primary-50 hover:text-primary-700 rounded-r transition-all duration-200 group relative"
                :class="{
                  'border-primary-500 bg-primary-50/50 text-primary-700': isActive(item.url),
                  'border-transparent hover:border-primary-300':
                    !isActive(item.url) && item.depth === 1,
                  'border-transparent hover:border-primary-200':
                    !isActive(item.url) && item.depth !== 1,
                  'font-semibold': item.depth === 1,
                  'text-sm': item.depth > 1,
                }"
                :style="{ paddingLeft: `${Math.max(item.depth - 1, 0) * 1 + 0.75}rem` }"
                @click="handleItemClick"
              >
                <span class="relative flex items-center">
                  <span
                    v-if="isActive(item.url)"
                    class="absolute -left-6 w-1 h-4 bg-primary-500 rounded-full animate-pulse"
                  ></span>
                  {{ item.title }}
                </span>
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'

interface TocItem {
  title: string
  url: string
  depth: number
}

interface ArticleTocProps {
  items: TocItem[]
  title?: string
  defaultOpen?: boolean
}

const props = withDefaults(defineProps<ArticleTocProps>(), {
  items: () => [],
  title: 'Contents',
  defaultOpen: true,
})

const mobileNavOpen = ref(props.defaultOpen)
const activeSection = ref('')

// Check if link is active
const isActive = (url: string): boolean => {
  return activeSection.value === url.replace('#', '')
}

// Handle click events
const handleItemClick = (): void => {
  // Close menu on mobile
  if (window.innerWidth < 768) {
    setTimeout(() => {
      mobileNavOpen.value = false
    }, 300)
  }
}

// Listen to scroll to update active section
const updateActiveSection = (): void => {
  const sections = props.items
    .map(item => {
      const id = item.url.replace('#', '')
      const element = document.getElementById(id)
      if (element) {
        const rect = element.getBoundingClientRect()
        return {
          id,
          top: rect.top,
          bottom: rect.bottom,
        }
      }
      return null
    })
    .filter(Boolean) as { id: string; top: number; bottom: number }[]

  // Find the section currently in viewport
  const viewportHeight = window.innerHeight
  const currentSection = sections.find(section => {
    return section.top <= viewportHeight * 0.3 && section.bottom >= 0
  })

  if (currentSection) {
    activeSection.value = currentSection.id
  }
}

// Debounce function
const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: ReturnType<typeof setTimeout> | null = null
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null
      func(...args)
    }
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(later, wait)
  }
}

const debouncedUpdateActiveSection = debounce(updateActiveSection, 100)

onMounted(() => {
  window.addEventListener('scroll', debouncedUpdateActiveSection)
  updateActiveSection()
})

onUnmounted(() => {
  window.removeEventListener('scroll', debouncedUpdateActiveSection)
})
</script>

<style scoped>
.article-toc-container {
  position: relative;
}

@media (min-width: 768px) {
  .article-toc-container {
    position: sticky;
    top: 1rem;
    max-height: calc(100vh - 2rem);
    overflow-y: auto;
  }
}

/* Custom scrollbar */
.article-toc-container::-webkit-scrollbar {
  width: 6px;
}

.article-toc-container::-webkit-scrollbar-track {
  background: var(--color-gray-100);
  border-radius: 3px;
}

.article-toc-container::-webkit-scrollbar-thumb {
  background: var(--color-primary-300);
  border-radius: 3px;
}

.article-toc-container::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary-400);
}

/* Firefox scrollbar */
.article-toc-container {
  scrollbar-width: thin;
  scrollbar-color: var(--color-primary-300) var(--color-gray-100);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles */
a:focus-visible {
  outline: 2px solid var(--color-primary-400);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Animations */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Responsive optimization */
@media (max-width: 767px) {
  .article-toc-container {
    position: relative;
    z-index: 10;
  }
}
</style>
