<template>
  <footer class="footer mt-auto">
    <div class="footer-wave">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 100" preserveAspectRatio="none">
        <defs>
          <linearGradient id="wave-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color:var(--color-primary);stop-opacity:1" />
            <stop offset="50%" style="stop-color:var(--color-tertiary);stop-opacity:1" />
            <stop offset="100%" style="stop-color:var(--color-secondary);stop-opacity:1" />
          </linearGradient>
        </defs>
        <path
          d="M0,32L80,42.7C160,53,320,75,480,74.7C640,75,800,53,960,42.7C1120,32,1280,32,1360,32L1440,32L1440,100L1360,100C1280,100,1120,100,960,100C800,100,640,100,480,100C320,100,160,100,80,100L0,100Z"
        ></path>
      </svg>
    </div>

    <div class="footer-content">
      <div class="footer-section about-section">
        <div class="footer-logo">SnaPFAS</div>
        <p class="footer-description">Synthetic Biology Innovation Project</p>
        <div class="social-icons">
          <a
            href="https://gitlab.igem.org/2025/basis-china"
            target="_blank"
            rel="noopener noreferrer"
            class="social-icon"
            title="GitLab"
            aria-label="GitLab"
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
              <path
                fill="currentColor"
                d="M22.65 14.39L12 22.13 1.35 14.39a.84.84 0 0 1-.3-.94l1.22-3.78 2.44-7.51A.42.42 0 0 1 4.82 2a.43.43 0 0 1 .58 0 .42.42 0 0 1 .11.18l2.44 7.49h8.1l2.44-7.51A.42.42 0 0 1 18.6 2a.43.43 0 0 1 .58 0 .42.42 0 0 1 .11.18l2.44 7.51L23 13.45a.84.84 0 0 1-.35.94z"
              ></path>
            </svg>
          </a>
          <a
            href="javascript:void(0)"
            @click="showWeChatInfo"
            class="social-icon"
            title="WeChat: BASIS iGEM"
            aria-label="WeChat"
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
              <path
                fill="currentColor"
                d="M8.691 2C4.768 2 1.554 4.635 1.5 7.972c-.044 2.729 1.45 4.947 3.985 5.965l-.507 1.858 2.267-1.32c.86.26 1.717.389 2.57.389.184 0 .37-.007.554-.02a5.228 5.228 0 0 1-.138-1.144c.037-3.45 3.097-6.233 6.9-6.233.184 0 .37.007.554.02C17.147 4.682 13.454 2 8.691 2zm-1.21 3.98c.512 0 .926.448.926.998 0 .55-.414.998-.926.998-.513 0-.926-.448-.926-.998 0-.55.413-.998.926-.998zm4.35 0c.513 0 .926.448.926.998 0 .55-.413.998-.926.998-.512 0-.926-.448-.926-.998 0-.55.414-.998.926-.998zM16.193 8.5c-3.045 0-5.507 2.34-5.507 5.226 0 2.886 2.462 5.226 5.507 5.226.645 0 1.261-.115 1.842-.321l1.691.963-.376-1.352c1.378-.838 2.35-2.213 2.35-3.836C21.7 10.84 19.238 8.5 16.193 8.5zm-1.752 2.536c.394 0 .714.335.714.747 0 .412-.32.747-.714.747-.393 0-.714-.335-.714-.747 0-.412.32-.747.714-.747zm3.336 0c.394 0 .714.335.714.747 0 .412-.32.747-.714.747-.393 0-.714-.335-.714-.747 0-.412.32-.747.714-.747z"
              ></path>
            </svg>
          </a>
        </div>
      </div>

      <div class="footer-section contact-section">
        <h3 class="footer-title">Contact Us</h3>
        <div class="contact-item">
          <span class="contact-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18">
              <path
                fill="currentColor"
                d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"
              ></path>
            </svg>
          </span>
          <span><EMAIL></span>
        </div>
        <div class="contact-item">
          <span class="contact-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18">
              <path
                fill="currentColor"
                d="M17 2H7c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 18H7V4h10v16z"
              ></path>
              <path
                fill="currentColor"
                d="M12 18c.83 0 1.5-.67 1.5-1.5S12.83 15 12 15s-1.5.67-1.5 1.5.67 1.5 1.5 1.5z"
              ></path>
            </svg>
          </span>
          <span>WeChat: BASIS iGEM</span>
        </div>
      </div>

      <div class="footer-section links-section">
        <h3 class="footer-title">Quick Links</h3>
        <div class="quick-links">
          <router-link to="/" class="footer-link">Home</router-link>
          <router-link to="/teammembers" class="footer-link">Team</router-link>
          <router-link to="/project/overview" class="footer-link">Project</router-link>
          <router-link to="/wet-lab/parts" class="footer-link">Parts</router-link>
          <router-link to="/wet-lab/safety" class="footer-link">Safety</router-link>
          <router-link to="/attribution" class="footer-link">Attributions</router-link>
        </div>
      </div>
    </div>

    <div class="sponsors-section">
      <h3 class="sponsors-title">Our Sponsors</h3>
      <div class="sponsors-logo-container">
        <a
          href="https://www.snapgene.com/"
          target="_blank"
          class="sponsor-logo-link"
          title="SnapGene"
        >
          <div class="sponsor-logo-container-white">
            <img
              src="https://static.igem.wiki/teams/5610/wiki/sponsorlogo/snapgene-logo.webp"
              alt="SnapGene Logo"
              class="sponsor-logo"
            />
          </div>
        </a>
        <a
          href="https://www.mathworks.com/"
          target="_blank"
          class="sponsor-logo-link"
          title="MathWorks"
        >
          <div class="sponsor-logo-container-white">
            <img
              src="https://static.igem.wiki/teams/5610/wiki/sponsorlogo/mathworks-logo-full-color-rgb.webp"
              alt="MathWorks Logo"
              class="sponsor-logo"
            />
          </div>
        </a>
      </div>
    </div>

    <div class="footer-bottom">
      <!-- iGEM required license information and repository link -->
      <div class="row mt-4">
        <div class="col">
          <p class="mb-0">
            <small>
              © {{ teamYear }} - Content on this site is licensed under a
              <a
                class="license-link"
                href="https://creativecommons.org/licenses/by/4.0/"
                rel="license"
                target="_blank"
              >
                Creative Commons Attribution 4.0 International license
              </a>
              .
            </small>
          </p>
          <p>
            <small>
              The repository used to create this website is available at
              <a
                class="repo-link"
                :href="`https://gitlab.igem.org/${teamYear}/${teamSlug}`"
                target="_blank"
              >
                gitlab.igem.org/{{ teamYear }}/{{ teamSlug }}
              </a>
              .
            </small>
          </p>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'

// Define component name to satisfy ESLint multi-word component naming rule
defineOptions({
  name: 'AppFooter',
})

// Use environment variables to get team information
const teamYear = import.meta.env.VITE_TEAM_YEAR as string
const teamSlug = import.meta.env.VITE_TEAM_NAME as string

// Show WeChat info when clicking WeChat icon
const showWeChatInfo = () => {
  message.info('WeChat Official Account: BASIS iGEM', 3)
}
</script>

<style scoped>
.footer {
  background: linear-gradient(to bottom, var(--color-neutral-900), #0a0a0a);
  color: white;
  padding: var(--space-8) var(--space-4);
  width: 100%;
  margin-top: auto;
  position: relative;
  overflow: hidden;
}

@media (min-width: 640px) {
  .footer {
    padding: var(--space-8) var(--space-5);
  }
}

/* Subtle background pattern */
.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(16, 185, 129, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(20, 184, 166, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.footer-wave {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  overflow: hidden;
  line-height: 0;
  transform: translateY(-100%);
  height: 60px;
}

.footer-wave svg {
  fill: url(#wave-gradient);
}

.footer-wave path {
  animation: wave-motion 20s ease-in-out infinite;
}

@keyframes wave-motion {
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(-50px); }
}

.footer-content {
  max-width: 80rem;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-7);
}

@media (min-width: 640px) {
  .footer-content {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .footer-content {
    grid-template-columns: repeat(3, 1fr);
  }
}

.footer-section {
  margin-bottom: var(--space-6);
}

@media (min-width: 1024px) {
  .footer-section {
    margin-bottom: 0;
  }
}

.about-section {
  display: flex;
  flex-direction: column;
}

.footer-logo {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  background: linear-gradient(135deg, var(--color-primary-light), var(--color-tertiary-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-3);
  letter-spacing: -0.02em;
  position: relative;
}

.footer-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-5);
  color: var(--color-neutral-200);
  position: relative;
  display: inline-block;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.footer-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 30px;
  height: 2px;
  background: linear-gradient(to right, var(--color-primary), transparent);
}

.footer-description {
  color: var(--color-neutral-400);
  margin-bottom: var(--space-5);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
}

.social-icons {
  display: flex;
  gap: var(--space-4);
  margin-top: var(--space-2);
}

.social-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-neutral-800);
  border-radius: var(--radius-lg);
  color: var(--color-neutral-400);
  transition: all var(--duration-normal) var(--ease-out);
  border: 1px solid var(--color-neutral-700);
}

.social-icon:hover {
  color: white;
  background: var(--color-primary);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);
  border-color: var(--color-primary);
}

.contact-section {
  display: flex;
  flex-direction: column;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-4);
  color: var(--color-neutral-400);
  transition: all var(--duration-normal) var(--ease-out);
  padding: var(--space-2) 0;
}

.contact-item:hover {
  color: var(--color-neutral-200);
  transform: translateX(4px);
}

.contact-icon {
  margin-right: var(--space-3);
  color: var(--color-primary-light);
}

.links-section {
  display: flex;
  flex-direction: column;
}

.quick-links {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  column-gap: var(--space-4);
  row-gap: var(--space-2);
}

.footer-link {
  color: var(--color-neutral-400);
  display: flex;
  align-items: center;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-base);
  transition: all var(--duration-normal) var(--ease-out);
  position: relative;
  overflow: hidden;
}

.footer-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.1), transparent);
  transition: left var(--duration-slow) var(--ease-out);
}

.footer-link:hover {
  color: var(--color-primary-light);
  background: rgba(16, 185, 129, 0.1);
}

.footer-link:hover::before {
  left: 100%;
}


.footer-bottom {
  max-width: 80rem;
  margin: 0 auto;
  padding-top: var(--space-6);
  margin-top: var(--space-8);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.footer-bottom::before {
  content: '';
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--color-primary), transparent);
}

.copyright {
  color: var(--color-neutral-400);
  text-align: center;
}

.license-link,
.repo-link {
  color: var(--color-primary-light);
  transition: color var(--duration-normal) var(--ease-in-out);
}

.license-link:hover,
.repo-link:hover {
  color: var(--color-primary-lighter);
}

.sponsors-section {
  max-width: 80rem;
  margin: 0 auto;
  padding: var(--space-7) 0;
  margin-top: var(--space-7);
  text-align: center;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.sponsors-section::before {
  content: '';
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--color-primary), transparent);
}

.sponsors-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-5);
  color: white;
}

.sponsors-logo-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: var(--space-6);
}

.sponsor-logo-link {
  transition:
    transform var(--duration-normal) var(--ease-in-out),
    box-shadow var(--duration-normal) var(--ease-in-out);
}

.sponsor-logo-link:hover {
  transform: translateY(-4px) scale(1.02);
}

.sponsor-logo-link:hover .sponsor-logo-container-white {
  box-shadow: var(--shadow-premium);
  background: white;
}

.sponsor-logo-container-white {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
  backdrop-filter: blur(10px);
  border-radius: var(--radius-lg);
  padding: var(--space-4) var(--space-6);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-elegant);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all var(--duration-normal) var(--ease-out);
  position: relative;
  overflow: hidden;
}

.sponsor-logo-container-white::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(16, 185, 129, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-out);
}

.sponsor-logo-container-white:hover::before {
  opacity: 1;
}

.sponsor-logo {
  height: 3.5rem;
  object-fit: contain;
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .quick-links {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 640px) {
  .footer-content {
    grid-template-columns: 1fr;
  }

  .footer-section {
    margin-bottom: var(--space-6);
  }

  .sponsor-logo {
    height: 3rem;
  }

  .sponsors-logo-container {
    gap: var(--space-5);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .footer {
    background-color: var(--color-neutral-900);
  }

  .footer-bottom {
    border-top-color: var(--color-neutral-800);
  }

  .sponsors-section {
    border-top-color: var(--color-neutral-800);
  }
}
</style>
