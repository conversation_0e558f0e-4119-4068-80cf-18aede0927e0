<template>
  <span v-html="renderedMath"></span>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { renderMath } from '../plugins/katex'

interface KatexMathProps {
  expression: string
  display?: boolean
}

const props = withDefaults(defineProps<KatexMathProps>(), {
  display: false,
})

const renderedMath = computed(() => {
  return renderMath(props.expression, props.display)
})
</script>
