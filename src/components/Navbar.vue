<template>
  <nav
    class="fixed w-full top-0 z-50 transition-all duration-500 ease-out bg-white/90 backdrop-blur-xl border-b"
    :class="[
      isScrolled 
        ? 'shadow-elegant border-neutral-200/50' 
        : 'shadow-soft border-neutral-100/50'
    ]"
  >
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <!-- Logo -->
        <div class="flex items-center">
          <router-link to="/" class="flex items-center space-x-3 group">
            <div
              class="w-10 h-10 overflow-hidden rounded-xl shadow-md transition-shadow duration-300 group-hover:shadow-lg"
            >
              <img 
                src="https://static.igem.wiki/teams/5610/wiki/icon/logo.webp" 
                alt="SnaPFAS Logo" 
                class="w-full h-full object-cover"
              />
            </div>
            <span class="font-bold text-xl text-neutral-900 transition-colors duration-300 group-hover:text-primary-600">
              SnaPFAS
            </span>
          </router-link>
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden md:flex items-center space-x-1">
          <router-link
            v-for="item in mainNavItems"
            :key="item.path"
            :to="item.path"
            class="px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200"
            :class="getLinkClass(item.path)"
          >
            {{ item.name }}
          </router-link>

          <!-- Dropdowns -->
          <div v-for="dropdown in dropdownItems" :key="dropdown.key" class="relative group">
            <button
              class="px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-1"
              :class="getDropdownClass(dropdown.key)"
            >
              <span>{{ dropdown.name }}</span>
              <ChevronDown class="w-4 h-4 transition-transform duration-200 group-hover:rotate-180" />
            </button>

            <!-- Dropdown Menu -->
            <div
              class="absolute top-full left-0 mt-2 w-56 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform scale-95 group-hover:scale-100 origin-top"
            >
              <div class="bg-white/95 backdrop-blur-xl rounded-xl shadow-premium border border-neutral-100/50 overflow-hidden">
                <div class="p-1">
                  <router-link
                    v-for="item in dropdown.items"
                    :key="item.path"
                    :to="item.path"
                    class="block px-4 py-2.5 rounded-lg text-sm text-neutral-700 hover:bg-primary-50 hover:text-primary-700 transition-colors duration-200"
                  >
                    {{ item.name }}
                  </router-link>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Mobile menu button -->
        <button
          @click="isMobileMenuOpen = !isMobileMenuOpen"
          class="md:hidden p-2 rounded-lg transition-all duration-300 text-neutral-900 hover:bg-neutral-100 hover:shadow-md active:scale-95"
        >
          <Menu v-if="!isMobileMenuOpen" class="w-6 h-6 transition-transform duration-300" />
          <X v-else class="w-6 h-6 transition-transform duration-300 rotate-90" />
        </button>
      </div>
    </div>

    <!-- Mobile menu -->
    <Transition
      enter-active-class="transition duration-300 ease-out"
      enter-from-class="transform scale-95 opacity-0"
      enter-to-class="transform scale-100 opacity-100"
      leave-active-class="transition duration-200 ease-in"
      leave-from-class="transform scale-100 opacity-100"
      leave-to-class="transform scale-95 opacity-0"
    >
      <div v-if="isMobileMenuOpen" class="md:hidden bg-white/95 backdrop-blur-xl border-t border-neutral-100/50 shadow-elegant">
        <div class="px-4 py-3 space-y-1">
          <router-link
            v-for="item in mainNavItems"
            :key="item.path"
            :to="item.path"
            @click="isMobileMenuOpen = false"
            class="block px-4 py-2.5 rounded-lg text-base font-medium text-neutral-700 hover:bg-primary-50 hover:text-primary-700 transition-colors duration-200"
          >
            {{ item.name }}
          </router-link>

          <div v-for="dropdown in dropdownItems" :key="dropdown.key" class="space-y-1">
            <div class="px-3 py-2 text-sm font-semibold text-slate-500">{{ dropdown.name }}</div>
            <router-link
              v-for="item in dropdown.items"
              :key="item.path"
              :to="item.path"
              @click="isMobileMenuOpen = false"
              class="block pl-6 pr-3 py-2 rounded-lg text-base font-medium text-slate-600 hover:bg-primary-50 hover:text-primary-700 transition-colors"
            >
              {{ item.name }}
            </router-link>
          </div>
        </div>
      </div>
    </Transition>
  </nav>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { Menu, X, ChevronDown } from 'lucide-vue-next'

const route = useRoute()
const isScrolled = ref(false)
const isMobileMenuOpen = ref(false)

const mainNavItems = [
  { name: 'Home', path: '/' },
  { name: 'Human Practices', path: '/human-practices' },
]

const dropdownItems = [
  {
    key: 'project',
    name: 'Project',
    items: [
      { name: 'Overview', path: '/project/overview' },
      { name: 'Design', path: '/project/design' },
    ],
  },
  {
    key: 'team',
    name: 'Team',
    items: [
      { name: 'Team Members', path: '/teammembers' },
      { name: 'Attribution', path: '/attribution' },
    ],
  },
  {
    key: 'wet-lab',
    name: 'Wet Lab',
    items: [
      { name: 'Experiment', path: '/wet-lab/experiment' },
      { name: 'Parts', path: '/wet-lab/parts' },
      { name: 'Results', path: '/wet-lab/results' },
      { name: 'Safety', path: '/wet-lab/safety' },
      { name: 'Engineering', path: '/wet-lab/engineering' },
    ],
  },
  {
    key: 'dry-lab',
    name: 'Dry Lab',
    items: [
      { name: 'Model', path: '/dry-lab/model' },
      { name: 'Hardware', path: '/dry-lab/hardware' },
      { name: 'Software', path: '/dry-lab/software' },
    ],
  },
]

const handleScroll = () => {
  isScrolled.value = window.scrollY > 10
}

const getLinkClass = (path: string) => {
  const isActive = route.path === path
  return isActive
    ? 'bg-primary-100 text-primary-700'
    : 'text-neutral-700 hover:bg-neutral-50 hover:text-neutral-900'
}

const getDropdownClass = (key: string) => {
  const isActive = dropdownItems
    .find(d => d.key === key)
    ?.items.some(item => route.path === item.path)

  return isActive
    ? 'bg-primary-100 text-primary-700'
    : 'text-neutral-700 hover:bg-neutral-50 hover:text-neutral-900'
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  handleScroll()
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>
