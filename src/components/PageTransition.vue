<template>
  <transition
    :name="name"
    :mode="mode"
    :appear="appear"
    @before-enter="beforeEnter"
    @enter="enter"
    @after-enter="afterEnter"
    @enter-cancelled="enterCancelled"
    @before-leave="beforeLeave"
    @leave="leave"
    @after-leave="afterLeave"
    @leave-cancelled="leaveCancelled"
  >
    <slot></slot>
  </transition>
</template>

<script setup lang="ts">
import { onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import gsap from 'gsap'

type TransitionMode = 'out-in' | 'in-out' | 'default'
type TransitionDirection = 'x' | 'y'

interface PageTransitionProps {
  name?: string
  mode?: TransitionMode
  appear?: boolean
  useGsap?: boolean
  duration?: number
  direction?: TransitionDirection
  distance?: number
}

const props = withDefaults(defineProps<PageTransitionProps>(), {
  name: 'fade',
  mode: 'out-in',
  appear: true,
  useGsap: true,
  duration: 0.3, // Use --duration-normal from design tokens
  direction: 'y',
  distance: 20,
})

// Transition hook functions
const beforeEnter = (el: Element): void => {
  if (!props.useGsap) return

  gsap.set(el, {
    autoAlpha: 0,
    [props.direction]: props.distance,
  })
}

const enter = (el: Element, done: () => void): void => {
  if (!props.useGsap) {
    done()
    return
  }

  gsap.to(el, {
    autoAlpha: 1,
    [props.direction]: 0,
    duration: props.duration,
    ease: 'power2.out',
    clearProps: 'transform',
    onComplete: done,
  })
}

const afterEnter = (el: Element): void => {
  // Clean up styles after transition
  if (props.useGsap) {
    gsap.set(el, { clearProps: 'all' })
  }
}

const enterCancelled = (el: Element): void => {
  // Clean up styles after transition is cancelled
  void el // Explicitly mark parameter as unused
}

const beforeLeave = (el: Element): void => {
  if (!props.useGsap) return

  gsap.set(el, {
    autoAlpha: 1,
    [props.direction]: 0,
  })
}

const leave = (el: Element, done: () => void): void => {
  if (!props.useGsap) {
    done()
    return
  }

  gsap.to(el, {
    autoAlpha: 0,
    [props.direction]: -props.distance,
    duration: props.duration,
    ease: 'power2.in',
    clearProps: 'transform',
    onComplete: done,
  })
}

const afterLeave = (el: Element): void => {
  // Clean up styles after transition
  if (props.useGsap) {
    gsap.set(el, { clearProps: 'all' })
  }
}

const leaveCancelled = (el: Element): void => {
  // Clean up styles after transition is cancelled
  void el // Explicitly mark parameter as unused
}

// Scroll to top when route changes
const route = useRoute()
watch(
  () => route.path,
  () => {
    // Use requestAnimationFrame to ensure smooth scrolling
    requestAnimationFrame(() => {
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      })
    })
  }
)

// AOS initialization (optional, if using AOS library)
onMounted(() => {
  // AOS initialization has been moved to individual page components
})
</script>

<style scoped>
/* Default CSS transition effects - using design tokens */
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--duration-normal) var(--ease-out);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Slide in from bottom */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
}

.slide-up-enter-from,
.slide-up-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

/* Slide in from top */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
}

.slide-down-enter-from,
.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* Slide in from right */
.slide-right-enter-active,
.slide-right-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
}

.slide-right-enter-from,
.slide-right-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* Slide in from left */
.slide-left-enter-active,
.slide-left-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
}

.slide-left-enter-from,
.slide-left-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

/* Scale effect */
.scale-enter-active,
.scale-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
}

.scale-enter-from,
.scale-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

/* Enhanced transition effects */
.page-enter-active,
.page-leave-active {
  transition: all var(--duration-normal) var(--ease-out);
}

.page-enter-from {
  opacity: 0;
  transform: translateY(var(--spacing-4));
}

.page-leave-to {
  opacity: 0;
  transform: translateY(calc(-1 * var(--spacing-4)));
}

/* Fast transition effects */
.fast-enter-active,
.fast-leave-active {
  transition: all var(--duration-fast) var(--ease-out);
}

.fast-enter-from,
.fast-leave-to {
  opacity: 0;
}

/* Slow transition effects */
.slow-enter-active,
.slow-leave-active {
  transition: all var(--duration-slow) var(--ease-out);
}

.slow-enter-from {
  opacity: 0;
  transform: translateY(var(--spacing-8));
}

.slow-leave-to {
  opacity: 0;
  transform: translateY(calc(-1 * var(--spacing-8)));
}

/* Ensure content doesn't overflow during transitions */
.fade-enter-active,
.fade-leave-active,
.slide-up-enter-active,
.slide-up-leave-active,
.slide-down-enter-active,
.slide-down-leave-active,
.slide-right-enter-active,
.slide-right-leave-active,
.slide-left-enter-active,
.slide-left-leave-active,
.scale-enter-active,
.scale-leave-active {
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Prevent flickering during transitions */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
