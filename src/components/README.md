# 组件使用指南

本项目采用按需导入的方式使用组件，以优化应用的初始加载性能。

## 组件导入最佳实践

### 1. 局部导入组件

所有组件都应该按需导入，而不是全局注册。这样可以：

- 减小初始包体积
- 提高应用启动速度
- 更好的类型推断支持
- 更清晰的依赖关系

### 2. 导入示例

#### Composition API（推荐）

```vue
<template>
  <div>
    <BaseButton variant="primary" @click="handleClick">点击我</BaseButton>
    <BaseCard title="示例卡片">
      <p>卡片内容</p>
    </BaseCard>
  </div>
</template>

<script setup lang="ts">
import BaseButton from '@/components/BaseButton.vue'
import BaseCard from '@/components/BaseCard.vue'

const handleClick = () => {
  console.log('按钮被点击')
}
</script>
```

#### Options API

```vue
<template>
  <div>
    <BaseBadge type="success">成功</BaseBadge>
    <SkeletonLoader :loading="isLoading">
      <div>实际内容</div>
    </SkeletonLoader>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import BaseBadge from '@/components/BaseBadge.vue'
import SkeletonLoader from '@/components/SkeletonLoader.vue'

export default defineComponent({
  components: {
    BaseBadge,
    SkeletonLoader,
  },
  data() {
    return {
      isLoading: true,
    }
  },
})
</script>
```

### 3. 可用组件列表

| 组件名称               | 描述           | 主要属性                         |
| ---------------------- | -------------- | -------------------------------- |
| BaseButton             | 基础按钮组件   | variant, size, disabled          |
| BaseCard               | 基础卡片组件   | title, subtitle, bordered        |
| BaseBadge              | 基础徽章组件   | type, size                       |
| SkeletonLoader         | 骨架屏加载组件 | loading, rows                    |
| HeroBanner             | 首页横幅组件   | title, subtitle, backgroundImage |
| SearchButton           | 搜索按钮组件   | variant                          |
| SearchModal            | 搜索模态框组件 | -                                |
| SearchHighlight        | 搜索高亮组件   | text, query                      |
| Navbar                 | 导航栏组件     | -                                |
| Footer                 | 页脚组件       | -                                |
| PageTransition         | 页面过渡组件   | name                             |
| ArticleTableOfContents | 文章目录组件   | headings                         |
| KatexMath              | 数学公式组件   | expression, displayMode          |

### 4. TypeScript 类型支持

每个组件都有对应的 TypeScript 类型定义，可以从 `@/types/components` 导入：

```typescript
import type { BaseButtonProps, BaseCardProps } from '@/types/components'
```

### 5. 注意事项

1. **不要使用全局组件注册**：避免在 `main.ts` 中使用 `app.component()` 注册组件
2. **使用路径别名**：使用 `@/components/` 而不是相对路径
3. **按需导入**：只导入实际使用的组件
4. **类型安全**：充分利用 TypeScript 的类型推断和检查

### 6. 性能优化建议

- 对于大型组件，考虑使用异步组件：

  ```typescript
  const HeroBanner = defineAsyncComponent(() => import('@/components/HeroBanner.vue'))
  ```

- 对于条件渲染的组件，使用 `v-if` 而不是 `v-show` 以避免不必要的组件初始化

### 7. 迁移指南

如果你的代码之前使用了全局组件，请按以下步骤迁移：

1. 在组件文件顶部添加导入语句
2. 如果使用 Options API，在 `components` 选项中注册组件
3. 移除任何关于全局组件的 TypeScript 声明

示例迁移：

```vue
<!-- 之前（全局组件） -->
<template>
  <BaseButton>点击</BaseButton>
</template>

<script setup lang="ts">
// 不需要导入
</script>

<!-- 之后（局部导入） -->
<template>
  <BaseButton>点击</BaseButton>
</template>

<script setup lang="ts">
import BaseButton from '@/components/BaseButton.vue'
</script>
```
