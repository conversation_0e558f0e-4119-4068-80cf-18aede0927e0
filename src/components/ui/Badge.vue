<template>
  <div :class="cn(badgeVariants({ variant: props.variant, size: props.size }), props.class)">
    <slot />
  </div>
</template>

<script setup lang="ts">
import { cn } from '@/lib/utils'
import { cva } from 'class-variance-authority'

const badgeVariants = cva(
  'inline-flex items-center font-medium transition-all duration-300 ease-out shadow-sm hover:shadow-md',
  {
    variants: {
      variant: {
        default:
          'bg-gradient-to-br from-primary-100 via-primary-100 to-primary-200 text-primary-700 hover:from-primary-200 hover:via-primary-200 hover:to-primary-300 border border-primary-200/50',
        secondary:
          'bg-gradient-to-br from-secondary-100 via-secondary-100 to-secondary-200 text-secondary-700 hover:from-secondary-200 hover:via-secondary-200 hover:to-secondary-300 border border-secondary-200/50',
        destructive:
          'bg-gradient-to-br from-red-100 via-red-100 to-red-200 text-red-700 hover:from-red-200 hover:via-red-200 hover:to-red-300 border border-red-200/50',
        outline: 'border-2 border-neutral-300 text-neutral-700 hover:bg-neutral-50 hover:border-neutral-400',
        success:
          'bg-gradient-to-br from-green-100 via-green-100 to-green-200 text-green-700 hover:from-green-200 hover:via-green-200 hover:to-green-300 border border-green-200/50',
        warning:
          'bg-gradient-to-br from-amber-100 via-amber-100 to-amber-200 text-amber-700 hover:from-amber-200 hover:via-amber-200 hover:to-amber-300 border border-amber-200/50',
        info:
          'bg-gradient-to-br from-blue-100 via-blue-100 to-blue-200 text-blue-700 hover:from-blue-200 hover:via-blue-200 hover:to-blue-300 border border-blue-200/50',
        glass:
          'bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20',
      },
      size: {
        sm: 'px-2 py-0.5 text-xs rounded-md',
        default: 'px-2.5 py-0.5 text-xs rounded-full',
        lg: 'px-3 py-1 text-sm rounded-full',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

interface Props {
  variant?: 'default' | 'secondary' | 'destructive' | 'outline' | 'success' | 'warning' | 'info' | 'glass'
  size?: 'sm' | 'default' | 'lg'
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  size: 'default'
})
</script>
