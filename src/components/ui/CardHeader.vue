<template>
  <div :class="cn('flex flex-col space-y-2', headerPadding, $attrs.class ?? '')">
    <slot />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { cn } from '@/lib/utils'

interface CardHeaderProps {
  noPadding?: boolean
  compact?: boolean
}

const props = withDefaults(defineProps<CardHeaderProps>(), {
  noPadding: false,
  compact: false,
})

const headerPadding = computed(() => {
  if (props.noPadding) return ''
  if (props.compact) return 'px-4 py-3'
  return 'px-6 py-5'
})
</script>
