import { ref, watchEffect } from 'vue'
import { useRoute } from 'vue-router'
import type { RouteLocationNormalizedLoaded } from 'vue-router'

export interface SEOMetaData {
  title: string
  description: string
  keywords: string[]
  ogImage?: string
  ogType?: string
  twitterCard?: string
  canonical?: string
  robots?: string
  author?: string
  jsonLd?: Record<string, unknown> | Array<Record<string, unknown>>
  twitterSite?: string
  ogLocale?: string
  articlePublishedTime?: string
  articleModifiedTime?: string
}

const DEFAULT_SEO: SEOMetaData = {
  title: 'SnaPFAS - BASIS-China iGEM 2025',
  description:
    'Sustainable Novel Actionable PFAS Degradation - A synthetic biology solution to forever chemicals by BASIS-China iGEM Team 2025',
  keywords: [
    'iGEM',
    'BASIS-China',
    'synthetic biology',
    'PFAS',
    'SnaPFAS',
    'biotechnology',
    'environmental',
    'sustainability',
  ],
  ogType: 'website',
  twitterCard: 'summary_large_image',
  robots: 'index, follow',
  author: 'BASIS-China iGEM Team 2025',
  // twitterSite: '@basis_china_igem', // No Twitter account
  ogLocale: 'en_US',
}

export function useSEO() {
  const route = useRoute()
  const currentMeta = ref<SEOMetaData>(DEFAULT_SEO)

  function updateMetaTags(meta: SEOMetaData) {
    // Update document title
    document.title = meta.title

    // Helper function to update or create meta tags
    const setMetaTag = (name: string, content: string, property = false) => {
      const attrName = property ? 'property' : 'name'
      let element = document.querySelector(`meta[${attrName}="${name}"]`) as HTMLMetaElement

      if (!element) {
        element = document.createElement('meta')
        element.setAttribute(attrName, name)
        document.head.appendChild(element)
      }

      element.content = content
    }

    // Basic meta tags
    setMetaTag('description', meta.description)
    setMetaTag('keywords', meta.keywords.join(', '))
    setMetaTag('author', meta.author || DEFAULT_SEO.author || '')
    setMetaTag('robots', meta.robots || DEFAULT_SEO.robots || '')

    // Open Graph tags
    setMetaTag('og:title', meta.title, true)
    setMetaTag('og:description', meta.description, true)
    setMetaTag('og:type', meta.ogType || DEFAULT_SEO.ogType || '', true)
    setMetaTag('og:site_name', 'BASIS-China iGEM 2025', true)

    if (meta.ogImage) {
      setMetaTag('og:image', meta.ogImage, true)
    }

    if (meta.canonical) {
      setMetaTag('og:url', meta.canonical, true)

      // Update canonical link
      let canonicalLink = document.querySelector('link[rel="canonical"]') as HTMLLinkElement
      if (!canonicalLink) {
        canonicalLink = document.createElement('link')
        canonicalLink.rel = 'canonical'
        document.head.appendChild(canonicalLink)
      }
      canonicalLink.href = meta.canonical
    }

    // Twitter Card tags
    setMetaTag('twitter:card', meta.twitterCard || DEFAULT_SEO.twitterCard || '')
    setMetaTag('twitter:title', meta.title)
    setMetaTag('twitter:description', meta.description)
    setMetaTag('twitter:site', meta.twitterSite || DEFAULT_SEO.twitterSite || '')
    if (meta.ogImage) {
      setMetaTag('twitter:image', meta.ogImage)
    }

    // Additional Open Graph tags
    setMetaTag('og:locale', meta.ogLocale || DEFAULT_SEO.ogLocale || '', true)
    if (meta.articlePublishedTime) {
      setMetaTag('article:published_time', meta.articlePublishedTime, true)
    }
    if (meta.articleModifiedTime) {
      setMetaTag('article:modified_time', meta.articleModifiedTime, true)
    }

    // JSON-LD structured data
    // Remove existing JSON-LD scripts
    const existingScripts = document.querySelectorAll(
      'script[type="application/ld+json"][data-vue-meta]'
    )
    existingScripts.forEach(script => script.remove())

    if (meta.jsonLd) {
      const jsonLdArray = Array.isArray(meta.jsonLd) ? meta.jsonLd : [meta.jsonLd]

      jsonLdArray.forEach(jsonLdItem => {
        const scriptElement = document.createElement('script')
        scriptElement.type = 'application/ld+json'
        scriptElement.setAttribute('data-vue-meta', 'true')
        scriptElement.textContent = JSON.stringify(jsonLdItem, null, 2)
        document.head.appendChild(scriptElement)
      })
    }
  }

  function setPageMeta(meta: Partial<SEOMetaData>) {
    currentMeta.value = { ...DEFAULT_SEO, ...meta }
    updateMetaTags(currentMeta.value)
  }

  function generateCanonicalUrl(route: RouteLocationNormalizedLoaded): string {
    const baseUrl = 'https://2025.igem.wiki/basis-china'
    return `${baseUrl}${route.path}`
  }

  // Watch for route changes and update meta accordingly
  watchEffect(() => {
    const canonical = generateCanonicalUrl(route)
    const routeMeta = route.meta.seo as SEOMetaData | undefined

    if (routeMeta) {
      setPageMeta({ ...routeMeta, canonical })
    } else {
      setPageMeta({ canonical })
    }
  })

  return {
    setPageMeta,
    currentMeta,
  }
}
