import type { SEOMetaData } from '@/composables/useSEO'

const baseUrl = 'https://2025.igem.wiki/basis-china'
const teamLogoUrl = 'https://static.igem.wiki/teams/5610/wiki/icon/logo.webp'
const teamPhotoUrl = 'https://static.igem.wiki/teams/5610/wiki/teamphoto/team/team.webp'
const defaultOgImage = 'https://2025.igem.wiki/basis-china/og-image.png'

// Helper to generate organization structured data
const generateOrganizationSchema = () => ({
  '@context': 'https://schema.org',
  '@type': 'Organization',
  name: 'BASIS-China iGEM Team',
  url: baseUrl,
  logo: teamLogoUrl,
  sameAs: ['https://gitlab.igem.org/2025/basis-china'],
  description: 'BASIS-China iGEM Team 2025 - Follow us on WeChat: BASIS iGEM',
  contactPoint: {
    '@type': 'ContactPoint',
    contactType: 'Social Media',
    availableLanguage: ['English', 'Chinese'],
    areaServed: 'CN',
  },
})

// Helper to generate research project structured data
const generateResearchProjectSchema = () => ({
  '@context': 'https://schema.org',
  '@type': 'ResearchProject',
  name: 'SnaPFAS - Sustainable Novel Actionable PFAS Degradation',
  description:
    'A synthetic biology approach to degrade per- and polyfluoroalkyl substances (PFAS) using engineered microorganisms',
  sponsor: {
    '@type': 'Organization',
    name: 'iGEM Foundation',
  },
  funder: [
    {
      '@type': 'Organization',
      name: 'BASIS International School China',
    },
  ],
  keywords: [
    'PFAS degradation',
    'synthetic biology',
    'environmental biotechnology',
    'bioremediation',
  ],
  startDate: '2025-01-01',
  endDate: '2025-11-30',
})

// Helper to generate FAQ structured data
const generateFAQSchema = (faqs: Array<{ question: string; answer: string }>) => ({
  '@context': 'https://schema.org',
  '@type': 'FAQPage',
  mainEntity: faqs.map(faq => ({
    '@type': 'Question',
    name: faq.question,
    acceptedAnswer: {
      '@type': 'Answer',
      text: faq.answer,
    },
  })),
})

// Helper to generate breadcrumb structured data
const generateBreadcrumbSchema = (items: Array<{ name: string; url: string }>) => ({
  '@context': 'https://schema.org',
  '@type': 'BreadcrumbList',
  itemListElement: items.map((item, index) => ({
    '@type': 'ListItem',
    position: index + 1,
    name: item.name,
    item: item.url,
  })),
})

// Helper to generate how-to structured data
const generateHowToSchema = (
  name: string,
  description: string,
  steps: Array<{ name: string; text: string }>
) => ({
  '@context': 'https://schema.org',
  '@type': 'HowTo',
  name,
  description,
  step: steps.map((step, index) => ({
    '@type': 'HowToStep',
    name: step.name,
    text: step.text,
    position: index + 1,
  })),
})

export const seoConfig: Record<string, Partial<SEOMetaData>> = {
  home: {
    title: 'SnaPFAS - BASIS-China iGEM 2025 | Sustainable PFAS Degradation',
    description:
      "Discover SnaPFAS: BASIS-China's innovative synthetic biology solution for degrading forever chemicals (PFAS) using engineered microorganisms. iGEM 2025 competition project. Follow us on WeChat: BASIS iGEM",
    keywords: [
      'SnaPFAS',
      'BASIS-China',
      'iGEM 2025',
      'PFAS degradation',
      'synthetic biology',
      'forever chemicals',
      'environmental biotechnology',
      'sustainable solutions',
      'BASIS iGEM',
      '微信公众号',
    ],
    ogImage: defaultOgImage,
    jsonLd: [
      {
        '@context': 'https://schema.org',
        '@type': 'WebSite',
        name: 'SnaPFAS - BASIS-China iGEM 2025',
        url: baseUrl,
        description: 'Sustainable synthetic biology solution for PFAS degradation',
        publisher: generateOrganizationSchema(),
        potentialAction: {
          '@type': 'SearchAction',
          target: `${baseUrl}/search?q={search_term_string}`,
          'query-input': 'required name=search_term_string',
        },
      },
      generateResearchProjectSchema(),
      generateBreadcrumbSchema([{ name: 'Home', url: baseUrl }]),
    ],
  },

  'team-members': {
    title: 'Team Members - BASIS-China iGEM 2025',
    description:
      'Meet the talented BASIS-China iGEM 2025 team members working on the SnaPFAS project. Students, advisors, and mentors dedicated to solving PFAS contamination.',
    keywords: [
      'BASIS-China team',
      'iGEM team members',
      'student researchers',
      'synthetic biology team',
      'PFAS research team',
    ],
    ogImage: teamPhotoUrl,
    jsonLd: [
      {
        '@context': 'https://schema.org',
        '@type': 'TeamPage',
        name: 'BASIS-China iGEM Team Members',
        description: 'The team behind the SnaPFAS project',
        publisher: generateOrganizationSchema(),
      },
      generateBreadcrumbSchema([
        { name: 'Home', url: baseUrl },
        { name: 'Team', url: `${baseUrl}/team` },
        { name: 'Members', url: `${baseUrl}/teammembers` },
      ]),
    ],
  },

  attribution: {
    title: 'Attribution - BASIS-China iGEM 2025',
    description:
      'Acknowledgments and attribution for the BASIS-China iGEM 2025 SnaPFAS project. Recognition of contributions, collaborations, and support.',
    keywords: ['attribution', 'acknowledgments', 'BASIS-China', 'iGEM 2025', 'collaborations'],
    jsonLd: {
      '@context': 'https://schema.org',
      '@type': 'WebPage',
      name: 'Attribution',
      description: 'Acknowledgments for the SnaPFAS project',
    },
  },

  'human-practices': {
    title: 'Human Practices - BASIS-China iGEM 2025',
    description:
      "Explore BASIS-China's community engagement, ethics considerations, and real-world impact assessment for the SnaPFAS PFAS degradation project.",
    keywords: [
      'human practices',
      'community engagement',
      'bioethics',
      'PFAS awareness',
      'public outreach',
      'responsible innovation',
      'stakeholder engagement',
      'environmental justice',
    ],
    jsonLd: [
      {
        '@context': 'https://schema.org',
        '@type': 'Article',
        headline: 'Human Practices in PFAS Degradation Research',
        description: 'Community engagement and ethical considerations in developing SnaPFAS',
        author: generateOrganizationSchema(),
        datePublished: '2025-09-01',
        articleSection: 'Human Practices',
      },
      generateBreadcrumbSchema([
        { name: 'Home', url: baseUrl },
        { name: 'Human Practices', url: `${baseUrl}/human-practices` },
      ]),
    ],
  },

  'project-overview': {
    title: 'Project Overview - SnaPFAS | BASIS-China iGEM 2025',
    description:
      'Comprehensive overview of SnaPFAS: an innovative synthetic biology approach to degrade PFAS chemicals using engineered microorganisms. Learn about our solution to forever chemicals.',
    keywords: [
      'SnaPFAS project',
      'PFAS degradation',
      'project overview',
      'synthetic biology solution',
      'environmental remediation',
    ],
    jsonLd: [
      generateResearchProjectSchema(),
      generateFAQSchema([
        {
          question: 'What is SnaPFAS?',
          answer:
            'SnaPFAS is a synthetic biology project that uses engineered microorganisms to degrade PFAS (per- and polyfluoroalkyl substances), also known as "forever chemicals".',
        },
        {
          question: 'How does SnaPFAS work?',
          answer:
            'SnaPFAS employs genetically modified bacteria that express specific enzymes capable of breaking down PFAS compounds into less harmful substances.',
        },
        {
          question: 'Why is PFAS degradation important?',
          answer:
            'PFAS are persistent environmental pollutants that accumulate in water, soil, and living organisms. They are linked to various health issues and do not break down naturally.',
        },
        {
          question: 'Is SnaPFAS safe?',
          answer:
            'Yes, SnaPFAS incorporates multiple biosafety features including containment systems and kill switches to ensure the engineered organisms cannot survive outside controlled conditions.',
        },
      ]),
      generateBreadcrumbSchema([
        { name: 'Home', url: baseUrl },
        { name: 'Project', url: `${baseUrl}/project` },
        { name: 'Overview', url: `${baseUrl}/project/overview` },
      ]),
    ],
  },

  'project-design': {
    title: 'Project Design - SnaPFAS | BASIS-China iGEM 2025',
    description:
      'Technical design and architecture of the SnaPFAS system. Explore genetic circuits, enzyme engineering, and biosafety measures for PFAS degradation.',
    keywords: [
      'project design',
      'genetic engineering',
      'biosensor design',
      'PFAS degradation pathway',
      'synthetic biology design',
    ],
    jsonLd: {
      '@context': 'https://schema.org',
      '@type': 'TechArticle',
      headline: 'Technical Design of SnaPFAS System',
      description: 'Detailed design of our PFAS degradation system',
      author: generateOrganizationSchema(),
    },
  },

  'wet-lab-experiment': {
    title: 'Wet Lab Experiments - BASIS-China iGEM 2025',
    description:
      'Detailed protocols and experimental procedures for the SnaPFAS project. Laboratory methods for engineering PFAS-degrading microorganisms.',
    keywords: [
      'wet lab',
      'experiments',
      'protocols',
      'laboratory methods',
      'PFAS degradation experiments',
    ],
    jsonLd: [
      {
        '@context': 'https://schema.org',
        '@type': 'Dataset',
        name: 'SnaPFAS Experimental Data',
        description: 'Laboratory experiments and protocols for PFAS degradation',
        creator: generateOrganizationSchema(),
        datePublished: '2025-10-01',
        license: 'https://creativecommons.org/licenses/by/4.0/',
      },
      generateHowToSchema(
        'PFAS Degradation Protocol',
        'Step-by-step protocol for degrading PFAS using engineered microorganisms',
        [
          { name: 'Culture Preparation', text: 'Prepare engineered E. coli cultures in LB medium' },
          {
            name: 'PFAS Treatment',
            text: 'Add PFAS substrate to culture at specified concentration',
          },
          { name: 'Incubation', text: 'Incubate cultures at 37°C with shaking' },
          { name: 'Analysis', text: 'Analyze PFAS degradation using LC-MS/MS' },
        ]
      ),
      generateBreadcrumbSchema([
        { name: 'Home', url: baseUrl },
        { name: 'Wet Lab', url: `${baseUrl}/wet-lab` },
        { name: 'Experiments', url: `${baseUrl}/wet-lab/experiment` },
      ]),
    ],
  },

  'wet-lab-parts': {
    title: 'BioBrick Parts - BASIS-China iGEM 2025',
    description:
      'Standardized BioBrick parts developed for SnaPFAS. Genetic components for PFAS detection and degradation available to the synthetic biology community.',
    keywords: [
      'BioBrick parts',
      'genetic parts',
      'iGEM registry',
      'PFAS degradation parts',
      'synthetic biology components',
    ],
    jsonLd: {
      '@context': 'https://schema.org',
      '@type': 'Dataset',
      name: 'SnaPFAS BioBrick Parts Collection',
      description: 'Standardized genetic parts for PFAS degradation',
    },
  },

  'wet-lab-results': {
    title: 'Experimental Results - BASIS-China iGEM 2025',
    description:
      'Scientific results and data from the SnaPFAS project. Evidence of successful PFAS degradation using engineered microorganisms.',
    keywords: [
      'experimental results',
      'PFAS degradation data',
      'scientific findings',
      'laboratory results',
      'biodegradation efficiency',
    ],
    jsonLd: {
      '@context': 'https://schema.org',
      '@type': 'ScholarlyArticle',
      headline: 'SnaPFAS Experimental Results',
      description: 'Results demonstrating PFAS degradation efficiency',
    },
  },

  'wet-lab-safety': {
    title: 'Lab Safety - BASIS-China iGEM 2025',
    description:
      'Comprehensive safety protocols and biosafety measures for the SnaPFAS project. BSL-1 compliance, PFAS handling procedures, risk assessment, and containment strategies ensuring safe synthetic biology research.',
    keywords: [
      'lab safety',
      'biosafety',
      'safety protocols',
      'risk assessment',
      'PFAS handling',
      'laboratory safety',
      'BSL-1',
      'PPE requirements',
      'chemical safety',
      'biological containment',
    ],
    jsonLd: [
      {
        '@context': 'https://schema.org',
        '@type': 'Article',
        headline: 'Safety Considerations in PFAS Degradation Research',
        description: 'Biosafety protocols and risk management',
        author: generateOrganizationSchema(),
        datePublished: '2025-06-01',
      },
      generateFAQSchema([
        {
          question: 'What biosafety level is required for SnaPFAS research?',
          answer:
            'SnaPFAS research is conducted in BSL-1 facilities as we use non-pathogenic E. coli strains with appropriate containment measures.',
        },
        {
          question: 'How do you safely handle PFAS compounds?',
          answer:
            'We follow strict protocols including fume hood use, proper PPE (gloves, lab coats, safety glasses), and specialized waste disposal for PFAS-contaminated materials.',
        },
      ]),
      generateBreadcrumbSchema([
        { name: 'Home', url: baseUrl },
        { name: 'Wet Lab', url: `${baseUrl}/wet-lab` },
        { name: 'Safety', url: `${baseUrl}/wet-lab/safety` },
      ]),
    ],
  },

  'wet-lab-engineering': {
    title: 'Engineering Success - BASIS-China iGEM 2025',
    description:
      'Engineering achievements in developing SnaPFAS. Iterative design process, optimization strategies, and breakthrough innovations in PFAS biodegradation.',
    keywords: [
      'engineering success',
      'design iteration',
      'optimization',
      'PFAS degradation engineering',
      'synthetic biology engineering',
    ],
    jsonLd: {
      '@context': 'https://schema.org',
      '@type': 'TechArticle',
      headline: 'Engineering Success in SnaPFAS Development',
      description: 'Iterative engineering process and achievements',
    },
  },

  'dry-lab-model': {
    title: 'Mathematical Modeling - BASIS-China iGEM 2025',
    description:
      'Computational models predicting SnaPFAS system behavior. Mathematical analysis of PFAS degradation kinetics, enzyme efficiency, and metabolic pathway optimization for enhanced biodegradation.',
    keywords: [
      'mathematical modeling',
      'computational biology',
      'PFAS degradation model',
      'kinetic modeling',
      'systems biology',
      'enzyme kinetics',
      'metabolic flux analysis',
      'ODE models',
    ],
    jsonLd: [
      {
        '@context': 'https://schema.org',
        '@type': 'ScholarlyArticle',
        headline: 'Mathematical Models of PFAS Degradation',
        description: 'Computational modeling of the SnaPFAS system',
        author: generateOrganizationSchema(),
        datePublished: '2025-10-01',
        keywords: 'PFAS degradation, mathematical modeling, enzyme kinetics',
        isPartOf: {
          '@type': 'PublicationIssue',
          name: 'iGEM 2025 Competition Projects',
        },
      },
      generateBreadcrumbSchema([
        { name: 'Home', url: baseUrl },
        { name: 'Dry Lab', url: `${baseUrl}/dry-lab` },
        { name: 'Model', url: `${baseUrl}/dry-lab/model` },
      ]),
    ],
  },

  'dry-lab-hardware': {
    title: 'Hardware Design - BASIS-China iGEM 2025',
    description:
      'Innovative hardware solutions for PFAS detection and degradation. Bioreactor design and sensor development for real-world SnaPFAS implementation.',
    keywords: [
      'hardware design',
      'bioreactor',
      'PFAS sensor',
      'detection hardware',
      'bioengineering hardware',
    ],
    jsonLd: {
      '@context': 'https://schema.org',
      '@type': 'TechArticle',
      headline: 'Hardware Solutions for PFAS Remediation',
      description: 'Physical devices supporting the SnaPFAS system',
    },
  },

  'dry-lab-software': {
    title: 'Software Tools - BASIS-China iGEM 2025',
    description:
      'Custom software and bioinformatics tools developed for SnaPFAS. Data analysis pipelines and design tools for PFAS degradation pathways.',
    keywords: [
      'software tools',
      'bioinformatics',
      'data analysis',
      'PFAS degradation software',
      'computational tools',
    ],
    jsonLd: {
      '@context': 'https://schema.org',
      '@type': 'SoftwareApplication',
      name: 'SnaPFAS Analysis Tools',
      description: 'Software for analyzing PFAS degradation data',
      applicationCategory: 'Bioinformatics',
    },
  },

  'not-found': {
    title: 'Page Not Found - BASIS-China iGEM 2025',
    description:
      'The requested page could not be found. Return to the SnaPFAS project homepage to explore our PFAS degradation research.',
    keywords: ['404', 'page not found', 'BASIS-China', 'iGEM 2025'],
    robots: 'noindex, follow',
  },
}
