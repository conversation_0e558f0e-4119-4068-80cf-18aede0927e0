# SEO Optimization Guide

## Overview

This document outlines the SEO optimization implementation for the BASIS-China iGEM 2025 SnaPFAS project wiki. The system provides comprehensive meta tag management, structured data, and dynamic SEO updates for all pages.

## Implementation Components

### 1. SEO Composable (`src/composables/useSEO.ts`)

A Vue composable that manages dynamic meta tag updates:

- Updates title, description, keywords dynamically
- Manages Open Graph (Facebook) meta tags
- Handles Twitter Card meta tags
- Injects JSON-LD structured data
- Generates canonical URLs automatically

### 2. SEO Configuration (`src/data/seoConfig.ts`)

Centralized SEO metadata for all routes:

- Unique title and description for each page
- Targeted keywords for each section
- JSON-LD structured data schemas
- Open Graph images configuration

### 3. Enhanced HTML Template (`index.html`)

Base SEO tags that apply site-wide:

- Primary meta tags (title, description, keywords)
- Open Graph meta tags for social sharing
- Twitter Card meta tags
- Canonical URL
- Theme color and language settings
- Base JSON-LD schema

### 4. Router Integration

SEO metadata is automatically applied on route changes through:

- Navigation guards that inject SEO config
- Automatic canonical URL generation
- Dynamic meta tag updates

## SEO Features by Page

### Home Page

- **Focus**: Project overview and team introduction
- **Keywords**: SnaPFAS, BASIS-China, iGEM 2025, PFAS degradation
- **Schema**: WebSite with SearchAction

### Team Pages

- **Team Members**: Team composition and expertise
- **Attribution**: Acknowledgments and collaborations
- **Schema**: TeamPage and Organization

### Project Pages

- **Overview**: Technical project description
- **Design**: Detailed architecture and approach
- **Schema**: ResearchProject

### Wet Lab Pages

- **Experiments**: Laboratory protocols
- **Parts**: BioBrick components
- **Results**: Scientific findings
- **Safety**: Biosafety measures
- **Engineering**: Iterative design process
- **Schema**: Dataset, ScholarlyArticle, TechArticle

### Dry Lab Pages

- **Model**: Mathematical modeling
- **Hardware**: Physical devices
- **Software**: Computational tools
- **Schema**: ScholarlyArticle, TechArticle, SoftwareApplication

### Human Practices

- **Focus**: Community engagement and ethics
- **Schema**: Article with author organization

## Technical Implementation

### Meta Tag Management

The system dynamically updates meta tags without page reload:

```typescript
// Example usage in a component
import { useSEO } from '@/composables/useSEO'

const { setPageMeta } = useSEO()

// Override default SEO for specific needs
setPageMeta({
  title: 'Custom Page Title',
  description: 'Custom description',
  keywords: ['custom', 'keywords'],
})
```

### Structured Data

Each page type has appropriate JSON-LD schemas:

- Organization schema for team identity
- ResearchProject for the main project
- ScholarlyArticle for scientific content
- SoftwareApplication for tools
- Dataset for experimental data

### Social Media Optimization

- Open Graph tags for Facebook sharing
- Twitter Card tags for Twitter sharing
- Default og:image for visual representation
- Page-specific images where appropriate

## Best Practices

1. **Unique Content**: Each page has unique title, description, and keywords
2. **Keyword Relevance**: Keywords match page content and user search intent
3. **Description Length**: Descriptions are 150-160 characters for optimal display
4. **Title Format**: Consistent format: "Page Topic - PROJECT NAME"
5. **Canonical URLs**: Automatically generated to prevent duplicate content issues

## Adding New Pages

When adding a new page:

1. Add route in `src/router/routes.ts`
2. Add SEO config in `src/data/seoConfig.ts`:
   ```typescript
   'route-name': {
     title: 'Page Title - BASIS-China iGEM 2025',
     description: 'Concise description (150-160 chars)',
     keywords: ['relevant', 'keywords'],
     jsonLd: { /* appropriate schema */ }
   }
   ```
3. The SEO system will automatically apply the configuration

## Monitoring SEO Performance

Recommended tools for monitoring:

- Google Search Console
- Google Rich Results Test
- Facebook Sharing Debugger
- Twitter Card Validator
- Lighthouse SEO audit

## Image Optimization

For optimal SEO:

- Create og:image at 1200x630px
- Use descriptive filenames
- Compress images appropriately
- Host on reliable CDN

## Future Enhancements

Potential improvements:

- Sitemap.xml generation
- Robots.txt optimization
- Multi-language support
- Schema.org breadcrumbs
- FAQ schema for Q&A content
