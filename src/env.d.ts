/// <reference types="vite/client" />

// Declare .vue file modules
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<Record<string, unknown>, Record<string, unknown>, unknown>
  export default component
}

// Declare Vite environment variable types
interface ImportMetaEnv {
  readonly VITE_TEAM_ID: string
  readonly VITE_TEAM_NAME: string
  readonly VITE_TEAM_YEAR: string
  readonly DEV: boolean
  readonly PROD: boolean
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
