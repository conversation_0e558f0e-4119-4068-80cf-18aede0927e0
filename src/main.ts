import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'
import { createPinia } from 'pinia'
import type { App as VueApp } from 'vue'
import { createApp } from 'vue'
import App from './App.vue'
import './assets/main.css'
import { setupKatex } from './plugins/katex'
import router from './router'

// Import AOS animation library
import AOS from 'aos'
import 'aos/dist/aos.css'

const app: VueApp = createApp(App)

// Create Pinia instance
const pinia = createPinia()

// Use plugins
app.use(pinia)
app.use(router)
app.use(Antd)
setupKatex(app)

// Initialize AOS
AOS.init({
  duration: 800,
  easing: 'ease-out',
  once: true,
  offset: 100,
})

app.mount('#app')
