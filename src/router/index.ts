import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router'
import { createRouter, createWebHistory } from 'vue-router'
import { routes } from './routes'
import { seoConfig } from '@/data/seoConfig'

// Get team information from environment variables
const TEAM_NAME = import.meta.env.VITE_TEAM_NAME || 'BASIS-China'

// Get team name from environment variables and convert to slug format as base path
function stringToSlug(str: string): string {
  if (!str) return ''
  return str
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')
    .replace(/[^\w-]+/g, '')
    .replace(/--+/g, '-')
    .replace(/^-+/, '')
    .replace(/-+$/, '')
}

// Create router, ensuring the base path is consistent with the configuration in vite.config.ts
const router = createRouter({
  history: createWebHistory(`/${stringToSlug(TEAM_NAME)}/`),
  routes,
})

// Global navigation guard for production optimizations and SEO
router.beforeEach(
  (to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) => {
    // Only log in development
    if (import.meta.env.DEV) {
      console.log(`[Router] Navigating from ${from.fullPath} to ${to.fullPath}`)
    }

    // Add SEO metadata to route meta
    const routeName = to.name as string
    if (routeName && seoConfig[routeName]) {
      to.meta.seo = seoConfig[routeName]
    }

    next()
  }
)

export default router
