# BASIS China 设计系统文档

## 概述

本设计系统为 BASIS China 项目提供一套统一、可扩展的设计基础。通过使用 CSS 自定义属性（CSS Variables）作为设计令牌，我们确保了整个应用的视觉一致性和易维护性。

## 设计令牌 (Design Tokens)

设计令牌是设计系统的基础构建块，定义在 `src/styles/design-tokens.css` 文件中。

### 间距系统 (Spacing System)

基于 8px 网格系统，提供一致的间距规范：

```css
--space-0: 0; /* 0px */
--space-1: 4px; /* 0.5x */
--space-2: 8px; /* 1x - 基础单位 */
--space-3: 12px; /* 1.5x */
--space-4: 16px; /* 2x */
--space-5: 24px; /* 3x */
--space-6: 32px; /* 4x */
--space-7: 48px; /* 6x */
--space-8: 64px; /* 8x */
```

**使用示例：**

```css
.card {
  padding: var(--space-4);
  margin-bottom: var(--space-5);
}
```

### 排版系统 (Typography System)

#### 字体大小

```css
--font-size-xs: 12px; /* 辅助文本、标签 */
--font-size-sm: 14px; /* 小号正文 */
--font-size-base: 16px; /* 基础正文 */
--font-size-lg: 18px; /* 大号正文 */
--font-size-xl: 20px; /* 小标题 */
--font-size-2xl: 24px; /* 中标题 */
--font-size-3xl: 32px; /* 大标题 */
--font-size-4xl: 48px; /* 特大标题 */
```

#### 行高

```css
--line-height-tight: 1.2; /* 紧凑，用于标题 */
--line-height-normal: 1.5; /* 正常，用于一般文本 */
--line-height-relaxed: 1.75; /* 宽松，用于正文 */
--line-height-loose: 2; /* 松散，用于特殊场景 */
```

#### 字重

```css
--font-weight-normal: 400; /* 正常 */
--font-weight-medium: 500; /* 中等 */
--font-weight-semibold: 600; /* 半粗 */
--font-weight-bold: 700; /* 粗体 */
```

#### 预定义排版类

```css
.text-h1    /* 48px, 1.2行高, 700字重 */
.text-h2    /* 32px, 1.2行高, 700字重 */
.text-h3    /* 24px, 1.5行高, 600字重 */
.text-h4    /* 20px, 1.5行高, 600字重 */
.text-h5    /* 18px, 1.5行高, 500字重 */

.text-body     /* 16px, 1.75行高, 400字重 */
.text-body-lg  /* 18px, 1.75行高, 400字重 */
.text-body-sm  /* 14px, 1.5行高, 400字重 */

.text-caption  /* 12px, 1.5行高, 次要颜色 */
.text-overline /* 12px, 1.5行高, 大写字母 */
```

### 颜色系统 (Color System)

#### 主题色

- **Primary（主色）** - 绿色系，用于主要操作和重要元素

  ```css
  --color-primary: #10b981;
  --color-primary-light: #34d399;
  --color-primary-dark: #059669;
  ```

- **Secondary（次要色）** - 蓝色系，用于次要操作和辅助元素

  ```css
  --color-secondary: #3b82f6;
  --color-secondary-light: #60a5fa;
  --color-secondary-dark: #2563eb;
  ```

- **Tertiary（第三色）** - 青色系，用于强调和装饰元素
  ```css
  --color-tertiary: #14b8a6;
  --color-tertiary-light: #2dd4bf;
  --color-tertiary-dark: #0d9488;
  ```

#### 语义化颜色

```css
/* 成功状态 - 使用主色 */
--color-success: var(--color-primary);

/* 信息状态 - 使用次要色 */
--color-info: var(--color-secondary);

/* 警告状态 - 橙色 */
--color-warning: #f59e0b;

/* 错误状态 - 红色 */
--color-error: #ef4444;
```

#### 文本颜色

```css
--color-text-primary: var(--color-neutral-900); /* 主要文本 */
--color-text-secondary: var(--color-neutral-700); /* 次要文本 */
--color-text-tertiary: var(--color-neutral-500); /* 辅助文本 */
--color-text-disabled: var(--color-neutral-400); /* 禁用文本 */
```

### 动画系统 (Animation System)

#### 时长

```css
--duration-fast: 150ms; /* 快速动画 */
--duration-normal: 300ms; /* 正常动画 */
--duration-slow: 500ms; /* 慢速动画 */
```

#### 缓动函数

```css
--ease-in-out: cubic-bezier(0.4, 0, 0.2, 1); /* 标准缓动 */
--ease-out: cubic-bezier(0, 0, 0.2, 1); /* 缓出 */
--ease-in: cubic-bezier(0.4, 0, 1, 1); /* 缓入 */
```

### 交互状态 (Interactive States)

使用 `.interactive` 类为元素添加统一的交互状态：

```html
<button class="interactive">点击我</button>
```

状态效果：

- **Hover**: 向上移动 1px，添加中等阴影
- **Focus**: 2px 主色边框，2px 偏移
- **Active**: 恢复位置，减小阴影
- **Disabled**: 50% 透明度，禁用指针事件

### 实用工具类 (Utility Classes)

#### 间距类

```css
.space-1 到 .space-8  /* 外边距 */
.pad-1 到 .pad-8      /* 内边距 */
```

#### 圆角类

```css
.radius-sm    /* 4px */
.radius-base  /* 8px */
.radius-md    /* 12px */
.radius-lg    /* 16px */
.radius-xl    /* 24px */
.radius-full  /* 9999px - 完全圆角 */
```

#### 阴影类

```css
.shadow-sm    /* 小阴影 */
.shadow-base  /* 基础阴影 */
.shadow-md    /* 中等阴影 */
.shadow-lg    /* 大阴影 */
.shadow-xl    /* 特大阴影 */
```

## 使用指南

### 1. 在组件中使用设计令牌

```vue
<template>
  <div class="card">
    <h2 class="text-h3">标题</h2>
    <p class="text-body">正文内容</p>
  </div>
</template>

<style scoped>
.card {
  padding: var(--space-4);
  border-radius: var(--radius-base);
  box-shadow: var(--shadow-base);
  background: var(--color-background);
}
</style>
```

### 2. 响应式设计

结合 Tailwind 的响应式前缀使用：

```html
<h1 class="text-h3 md:text-h2 lg:text-h1">响应式标题</h1>
```

### 3. 深色模式支持

设计令牌自动支持深色模式，无需额外配置：

```css
/* 自动适应深色模式 */
.element {
  color: var(--color-text-primary);
  background: var(--color-background);
}
```

## 最佳实践

1. **优先使用设计令牌**：始终使用 CSS 变量而不是硬编码值
2. **保持一致性**：使用预定义的排版类和间距系统
3. **语义化命名**：使用语义化颜色变量（如 `--color-success`）而不是具体颜色
4. **组合使用**：将设计令牌与 Tailwind 类结合使用，获得最佳灵活性

## 维护和扩展

- 所有设计令牌定义在 `src/styles/design-tokens.css`
- 排版和工具类定义在 `src/assets/main.css`
- 需要添加新令牌时，请遵循现有的命名规范
- 确保新增的令牌同时支持亮色和深色模式
