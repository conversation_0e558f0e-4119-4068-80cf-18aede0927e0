# 视觉设计规范

本文档提供了BASIS CHINA iGEM项目的视觉设计指南，确保整个应用程序具有一致且专业的外观。

## 目录

1. [页面布局最佳实践](#页面布局最佳实践)
2. [响应式设计指南](#响应式设计指南)
3. [交互状态设计原则](#交互状态设计原则)
4. [微交互和动画使用建议](#微交互和动画使用建议)
5. [可访问性考虑](#可访问性考虑)

## 页面布局最佳实践

### 1. 视觉层次

- **标题层级**：使用清晰的标题层级（h1 > h2 > h3）来组织内容

  ```css
  h1: text-3xl sm:text-4xl md:text-5xl
  h2: text-2xl sm:text-2xl md:text-3xl
  h3: text-xl sm:text-xl md:text-2xl
  ```

- **间距系统**：使用一致的间距来创建视觉节奏

  - 节之间：`mb-8 sm:mb-10 md:mb-12`
  - 段落之间：`mb-4 sm:mb-6`
  - 元素之间：`space-y-4` 或 `gap-4`

- **视觉分组**：使用卡片、背景色和边框来分组相关内容
  ```vue
  <section
    class="bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
  >
    <!-- 内容 -->
  </section>
  ```

### 2. 内容对齐

- **中心对齐**：用于标题和引导性内容
- **左对齐**：用于正文和详细信息
- **网格对齐**：使用网格系统确保元素对齐
  ```css
  grid-cols-1 sm:grid-cols-2 lg:grid-cols-4
  ```

### 3. 颜色使用

- **主色调**：用于重要操作和强调元素
- **中性色**：用于正文和背景
- **语义色**：用于状态反馈（成功、警告、错误）

## 响应式设计指南

### 1. 断点系统

```css
/* 移动优先的断点 */
xs: 475px   /* 超小屏幕 */
sm: 640px   /* 小屏幕 */
md: 768px   /* 中等屏幕 */
lg: 1024px  /* 大屏幕 */
xl: 1280px  /* 超大屏幕 */
```

### 2. 响应式文字

```css
/* 标题文字大小 */
.responsive-heading {
  @apply text-2xl xs:text-3xl sm:text-4xl md:text-5xl;
}

/* 正文文字大小 */
.responsive-text {
  @apply text-sm xs:text-base sm:text-lg;
}
```

### 3. 响应式间距

```css
/* 内边距 */
.responsive-padding {
  @apply p-4 sm:p-6 md:p-8;
}

/* 外边距 */
.responsive-margin {
  @apply mb-6 sm:mb-8 md:mb-12;
}
```

### 4. 移动端优化

- **触摸目标**：确保可点击元素至少有 44x44px 的触摸区域
- **滚动优化**：使用 `-webkit-overflow-scrolling: touch` 实现平滑滚动
- **视口设置**：确保正确的视口元标签

## 交互状态设计原则

### 1. 悬停状态 (Hover)

```css
/* 基础悬停效果 */
.hover-effect {
  transition: all 0.2s ease-out;
}

.hover-effect:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 颜色变化 */
.text-gray-700 hover:text-primary-700
.bg-white hover:bg-primary-50
```

### 2. 焦点状态 (Focus)

```css
/* 统一的焦点样式 */
:focus-visible {
  outline: 2px solid var(--color-primary-400);
  outline-offset: 2px;
  border-radius: 4px;
}
```

### 3. 激活状态 (Active)

```css
/* 按下效果 */
:active {
  transform: scale(0.98);
  transition: transform 0.1s ease-out;
}
```

### 4. 禁用状态 (Disabled)

```css
:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}
```

### 5. 加载状态

- 使用骨架屏组件 `<SkeletonLoader>`
- 显示加载指示器
- 保持布局稳定，避免内容跳动

## 微交互和动画使用建议

### 1. 动画时长

使用设计令牌中定义的时长：

- **快速**：`var(--duration-fast)` - 150ms（用于小元素）
- **正常**：`var(--duration-normal)` - 300ms（用于大多数过渡）
- **慢速**：`var(--duration-slow)` - 500ms（用于复杂动画）

### 2. 缓动函数

```css
/* 进入动画 */
ease-out: cubic-bezier(0, 0, 0.2, 1)

/* 退出动画 */
ease-in: cubic-bezier(0.4, 0, 1, 1)

/* 进出动画 */
ease-in-out: cubic-bezier(0.4, 0, 0.2, 1)
```

### 3. 常用动画效果

#### 淡入淡出

```css
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
```

#### 滑入效果

```css
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
```

#### 缩放效果

```css
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
```

### 4. 性能优化

- 使用 `transform` 和 `opacity` 进行动画
- 添加 `will-change` 属性优化性能
- 避免同时动画过多元素
- 使用 `requestAnimationFrame` 进行复杂动画

### 5. 页面过渡

使用 `PageTransition.vue` 组件实现统一的页面过渡效果：

```vue
<PageTransition name="fade" :duration="0.3">
  <router-view />
</PageTransition>
```

## 可访问性考虑

### 1. 颜色对比度

- 确保文字与背景的对比度符合 WCAG AA 标准
- 正文：至少 4.5:1
- 大文字：至少 3:1

### 2. 键盘导航

- 所有交互元素都应可通过键盘访问
- 提供清晰的焦点指示器
- 支持 Tab 键导航顺序

### 3. 屏幕阅读器

- 使用语义化 HTML
- 提供适当的 ARIA 标签
- 确保图片有替代文本

### 4. 动画偏好

```css
/* 尊重用户的动画偏好 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
```

## 组件特定指南

### 卡片组件

```vue
<div
  class="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100"
>
  <!-- 卡片内容 -->
</div>
```

### 按钮组件

```vue
<button
  class="px-6 py-3 bg-primary-600 text-white rounded-xl hover:bg-primary-700 focus:ring-4 focus:ring-primary-400/50 transition-all duration-200"
>
  按钮文本
</button>
```

### 导航链接

```vue
<router-link class="text-gray-700 hover:text-primary-700 transition-colors duration-200">
  链接文本
</router-link>
```

## 最佳实践总结

1. **一致性**：在整个应用中保持视觉元素的一致性
2. **层次性**：使用大小、颜色和间距创建清晰的视觉层次
3. **响应性**：确保设计在所有设备上都能良好工作
4. **性能**：优化动画和过渡以确保流畅的用户体验
5. **可访问性**：确保所有用户都能访问和使用应用
6. **反馈**：为用户操作提供即时的视觉反馈
7. **简洁**：避免过度设计，保持界面清晰简洁

通过遵循这些指南，我们可以创建一个美观、一致且用户友好的界面，提升整体用户体验。
