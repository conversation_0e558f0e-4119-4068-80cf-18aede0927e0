/**
 * Component type definitions
 */

// HeroBanner component types
export type HeroBannerVariant = 'primary' | 'secondary' | 'tertiary' | 'dark' | 'light' | 'gradient' | 'custom'

export interface HeroBannerProps {
  variant?: HeroBannerVariant
  fullHeight?: boolean
  useAltFont?: boolean
  lightText?: boolean
  backgroundImage?: string
  particles?: boolean
  wave?: boolean
  blur?: boolean
  animation?: boolean
  customBackground?: string
}

// PageTransition component types
export type TransitionMode = 'out-in' | 'in-out' | 'default'
export type TransitionDirection = 'x' | 'y'

export interface PageTransitionProps {
  name?: string
  mode?: TransitionMode
  appear?: boolean
  useGsap?: boolean
  duration?: number
  direction?: TransitionDirection
  distance?: number
}

// SkeletonLoader component types
export type SkeletonType = 'header' | 'card' | 'text' | 'list' | 'table' | 'custom'
export type ShadowLevel = 'none' | 'sm' | 'md' | 'lg' | 'xl'

export interface SkeletonLoaderProps {
  type?: SkeletonType
  lines?: number
  columns?: number
  animate?: boolean
  image?: boolean
  avatar?: boolean
  shadow?: ShadowLevel
}

// ArticleTableOfContents component types
export interface TocItem {
  title: string
  url: string
  depth: number
}

export interface ArticleTocProps {
  items: TocItem[]
  title?: string
  defaultOpen?: boolean
}

// KatexMath component types
export interface KatexMathProps {
  expression: string
  display?: boolean
}