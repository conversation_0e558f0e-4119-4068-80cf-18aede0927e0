/**
 * Route-related type definitions
 */

import type { Component } from 'vue'
import type { SEOMetaData } from '@/composables/useSEO'

/**
 * Route metadata interface
 */
export interface RouteMeta {
  /** Page title */
  title: string
  /** Page category */
  category: 'home' | 'team' | 'project' | 'wet-lab' | 'dry-lab' | 'human-practices' | 'other'
  /** Whether authentication is required (reserved) */
  requiresAuth?: boolean
  /** Page description (for SEO) */
  description?: string
  /** Page keywords (for SEO) */
  keywords?: string[]
  /** SEO metadata */
  seo?: Partial<SEOMetaData>
}

/**
 * Route configuration interface
 * Extends Vue Router route definition
 */
export interface RouteConfig {
  /** Route path */
  path: string
  /** Route name */
  name: string
  /** Route component */
  component: Component
  /** Route metadata */
  meta?: RouteMeta
  /** Child routes */
  children?: RouteConfig[]
  /** Route redirect */
  redirect?: string
}

/**
 * Declare Vue Router route metadata types
 */
declare module 'vue-router' {
  interface RouteMeta extends RouteMeta {}
}
