<template>
  <div class="attribution-container pt-8 w-full">
    <div class="absolute inset-0 bg-gray-50 w-full h-full"></div>
    <div class="relative z-10 w-full max-w-7xl mx-auto">
      <div class="max-w-3xl mx-auto mb-8 sm:mb-12 md:mb-16 px-4">
        <h1
          class="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6 text-center text-gray-900"
        >
          Attribution
        </h1>
        <div
          class="w-20 h-1 bg-gradient-to-r from-primary-400 to-secondary-400 mx-auto mb-6 rounded-full"
        ></div>
        <p class="text-base sm:text-lg md:text-xl text-gray-600 text-center leading-relaxed">
          We acknowledge and thank all the individuals, organizations, and resources that
          contributed to our project.
        </p>
      </div>

      <!-- Mobile Table of Contents -->
      <div class="block md:hidden max-w-7xl mx-auto px-4 mb-6">
        <ArticleTableOfContents :items="tocItems" title="Contents" :default-open="false" />
      </div>

      <div class="flex flex-col md:flex-row max-w-7xl mx-auto px-4 sm:px-6">
        <!-- Desktop Table of Contents -->
        <div class="hidden md:block w-64 h-screen sticky top-0 overflow-y-auto p-4">
          <ArticleTableOfContents :items="tocItems" title="Contents" />
        </div>

        <!-- Main Content -->
        <div class="flex-1 md:pl-8 bg-gray-50 min-h-screen">
          <article class="max-w-4xl space-y-8 sm:space-y-10 md:space-y-12">
            <section
              id="software"
              class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
            >
              <h2
                class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
              >
                <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
                Software
              </h2>
              <div class="space-y-6 sm:space-y-8 pl-4">
                <div class="software-card">
                  <h3
                    class="text-xl sm:text-xl md:text-2xl font-semibold text-primary-600 mb-3 sm:mb-4"
                  >
                    Development Tools
                  </h3>
                  <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-6">
                    <div class="tool-item">
                      <div class="flex items-start mb-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-5 w-5 text-primary-500 mr-2 mt-0.5 flex-shrink-0"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z"
                            clip-rule="evenodd"
                          />
                        </svg>
                        <h4 class="text-base sm:text-lg font-medium leading-tight">Vue.js</h4>
                      </div>
                      <p class="text-sm sm:text-base text-gray-600">
                        Frontend framework for building user interfaces
                      </p>
                    </div>
                    <div class="tool-item">
                      <div class="flex items-start mb-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-5 w-5 text-primary-500 mr-2 mt-0.5 flex-shrink-0"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z"
                            clip-rule="evenodd"
                          />
                        </svg>
                        <h4 class="text-lg font-medium leading-tight">Tailwind CSS</h4>
                      </div>
                      <p class="text-gray-600">Utility-first CSS framework</p>
                    </div>
                    <div class="tool-item">
                      <div class="flex items-start mb-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-5 w-5 text-primary-500 mr-2 mt-0.5 flex-shrink-0"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z"
                            clip-rule="evenodd"
                          />
                        </svg>
                        <h4 class="text-lg font-medium leading-tight">Vite</h4>
                      </div>
                      <p class="text-gray-600">Next generation frontend tooling</p>
                    </div>
                  </div>
                </div>

                <div class="software-card">
                  <h3
                    class="text-xl sm:text-xl md:text-2xl font-semibold text-primary-600 mb-3 sm:mb-4"
                  >
                    Research Tools
                  </h3>
                  <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-6">
                    <div class="tool-item">
                      <div class="flex items-start mb-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-5 w-5 text-primary-500 mr-2 mt-0.5 flex-shrink-0"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8zM12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z"
                          />
                        </svg>
                        <h4 class="text-base sm:text-lg font-medium leading-tight">SnapGene</h4>
                      </div>
                      <p class="text-sm sm:text-base text-gray-600">
                        Software for designing, visualizing, and documenting molecular biology
                        experiments
                      </p>
                    </div>
                    <div class="tool-item">
                      <div class="flex items-start mb-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-5 w-5 text-primary-500 mr-2 mt-0.5 flex-shrink-0"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            d="M10 3.5a1.5 1.5 0 013 0V4a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-.5a1.5 1.5 0 000 3h.5a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-.5a1.5 1.5 0 00-3 0v.5a1 1 0 01-1 1H6a1 1 0 01-1-1v-3a1 1 0 00-1-1h-.5a1.5 1.5 0 010-3H4a1 1 0 001-1V6a1 1 0 011-1h3a1 1 0 001-1v-.5z"
                          />
                        </svg>
                        <h4 class="text-base sm:text-lg font-medium leading-tight">MATLAB</h4>
                      </div>
                      <p class="text-sm sm:text-base text-gray-600">
                        Platform for analyzing data, developing algorithms, and creating models for
                        scientific and engineering applications
                      </p>
                    </div>
                  </div>
                </div>

                <div class="software-card">
                  <h3
                    class="text-xl sm:text-xl md:text-2xl font-semibold text-primary-600 mb-3 sm:mb-4"
                  >
                    Design Tools
                  </h3>
                  <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-6">
                    <div class="tool-item">
                      <div class="flex items-start mb-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-5 w-5 text-primary-500 mr-2 mt-0.5 flex-shrink-0"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                            clip-rule="evenodd"
                          />
                        </svg>
                        <h4 class="text-base sm:text-lg font-medium leading-tight">Figma</h4>
                      </div>
                      <p class="text-sm sm:text-base text-gray-600">Interface design tool</p>
                    </div>
                    <!-- Add more design tools as needed -->
                  </div>
                </div>
              </div>
            </section>

            <section
              id="resources"
              class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
            >
              <h2
                class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
              >
                <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
                Resources
              </h2>
              <div class="space-y-4 sm:space-y-6 pl-4">
                <div class="resource-card">
                  <h3
                    class="text-xl sm:text-xl md:text-2xl font-semibold text-primary-600 mb-3 sm:mb-4"
                  >
                    Research Papers
                  </h3>
                  <ul class="space-y-4">
                    <li class="flex items-start">
                      <span
                        class="inline-block w-2 h-2 rounded-full bg-primary-500 mt-2 mr-3"
                      ></span>
                      <span class="text-base sm:text-lg text-gray-700">
                        [Paper Title] - [Authors] ([Year])
                      </span>
                    </li>
                    <!-- Add more papers as needed -->
                  </ul>
                </div>
              </div>
            </section>

            <section
              id="sponsors"
              class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
            >
              <h2
                class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
              >
                <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
                Sponsors
              </h2>
              <div class="space-y-4 sm:space-y-6 pl-4">
                <p class="text-base sm:text-lg text-gray-700 leading-relaxed">
                  We are grateful to our sponsors who have provided valuable support for our iGEM
                  project:
                </p>
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 sm:gap-8">
                  <div
                    class="sponsor-card p-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
                  >
                    <div class="flex flex-col items-center h-full justify-between">
                      <div class="flex flex-col items-center">
                        <div
                          class="sponsor-logo-container bg-white p-4 rounded-lg mb-4 w-full flex items-center justify-center"
                        >
                          <img
                            src="https://static.igem.wiki/teams/5610/wiki/sponsorlogo/snapgene-logo.webp"
                            alt="SnapGene Logo"
                            class="h-16 object-contain"
                          />
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">SnapGene</h3>
                        <div class="description-container h-24 overflow-hidden">
                          <p class="text-gray-600 text-center text-sm">
                            SnapGene provides intuitive software for molecular biology, making it
                            easier to plan, visualize, and document DNA cloning and PCR experiments.
                          </p>
                        </div>
                      </div>
                      <a
                        href="https://www.snapgene.com/"
                        target="_blank"
                        class="mt-3 text-primary-600 hover:text-primary-700 transition-colors duration-300 text-sm font-medium"
                      >
                        Visit Website →
                      </a>
                    </div>
                  </div>
                  <div
                    class="sponsor-card p-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
                  >
                    <div class="flex flex-col items-center h-full justify-between">
                      <div class="flex flex-col items-center">
                        <div
                          class="sponsor-logo-container bg-white p-4 rounded-lg mb-4 w-full flex items-center justify-center"
                        >
                          <img
                            src="https://static.igem.wiki/teams/5610/wiki/sponsorlogo/mathworks-logo-full-color-rgb.webp"
                            alt="MathWorks Logo"
                            class="h-16 object-contain"
                          />
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">MathWorks</h3>
                        <div class="description-container h-24 overflow-hidden">
                          <p class="text-gray-600 text-center text-sm">
                            MathWorks is the leading developer of mathematical computing software,
                            providing engineers and scientists with powerful tools for simulation,
                            modeling, and data analysis.
                          </p>
                        </div>
                      </div>
                      <a
                        href="https://www.mathworks.com/"
                        target="_blank"
                        class="mt-3 text-primary-600 hover:text-primary-700 transition-colors duration-300 text-sm font-medium"
                      >
                        Visit Website →
                      </a>
                    </div>
                  </div>
                  <!-- Add more sponsors cards here as needed -->
                </div>
              </div>
            </section>

            <section
              id="special-thanks"
              class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
            >
              <h2
                class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
              >
                <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
                Special Thanks
              </h2>
              <div class="space-y-4 sm:space-y-6 pl-4">
                <p class="text-base sm:text-lg text-gray-700 leading-relaxed">
                  We would like to express our gratitude to the following individuals and
                  organizations for their support and contributions to our project:
                </p>
                <ul class="space-y-4">
                  <li class="flex items-start">
                    <span class="inline-block w-2 h-2 rounded-full bg-primary-500 mt-2 mr-3"></span>
                    <span class="text-base sm:text-lg text-gray-700">
                      [Name/Organization] - [Contribution]
                    </span>
                  </li>
                  <!-- Add more acknowledgments as needed -->
                </ul>
              </div>
            </section>

            <!--
            ======================================================================
            == VERY IMPORTANT                                                   ==
            ======================================================================
            LEAVE THE IFRAME CODE BELOW AS IT IS, THE ATTRIBUTION FORM OF YOUR TEAM
            WILL BE DISPLAYED ON THIS PAGE. DO NOT REMOVE IT, OTHERWISE YOU RISK OF
            NOT MEETING BRONZE MEDAL CRITERION #2
          -->

            <section
              id="attribution-form"
              class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
            >
              <h2
                class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
              >
                <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
                Team Attribution Form
              </h2>
              <div class="space-y-4 sm:space-y-6 pl-4">
                <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-6">
                  This form documents the contributions of each team member to our iGEM project. It
                  ensures transparency and fair recognition of everyone's efforts in accordance with
                  iGEM competition requirements.
                </p>
                <div class="iframe-container rounded-lg overflow-hidden border border-gray-200">
                  <iframe
                    id="igem-attribution-form"
                    :src="`https://teams.igem.org/wiki/${teamID}/attributions`"
                    class="w-full min-h-[600px] sm:min-h-[700px] md:min-h-[800px]"
                    frameborder="0"
                    scrolling="yes"
                    allowfullscreen
                  />
                </div>
              </div>
            </section>
            <!-- ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ -->
          </article>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Add any necessary imports and logic here
import { ref } from 'vue'
import ArticleTableOfContents from '@/components/ArticleTableOfContents.vue'

// Get team ID from environment variable
const teamID = ref(import.meta.env.VITE_TEAM_ID || '5610')

// Table of contents items
const tocItems = ref([
  { title: 'Software', url: '#software', depth: 1 },
  { title: 'Resources', url: '#resources', depth: 1 },
  { title: 'Sponsors', url: '#sponsors', depth: 1 },
  { title: 'Special Thanks', url: '#special-thanks', depth: 1 },
  { title: 'Attribution Form', url: '#attribution-form', depth: 1 },
])
</script>

<style scoped>
.attribution-container {
  min-height: calc(100vh - 64px); /* Adjust based on your header height */
  background-color: rgb(249, 250, 251); /* bg-gray-50 */
  padding-top: 4rem; /* Increased top padding to avoid overlap with navigation */
  padding-bottom: 2rem;
}

.software-card,
.resource-card {
  padding: 1rem;
  border-radius: 0.75rem;
  border: 2px solid #f3f4f6;
  transition: border-color 0.2s;
  background-color: white;
}

.tool-item {
  padding: 0.75rem;
  border-radius: 0.5rem;
  background-color: #f9fafb;
  transition: background-color 0.2s;
}

.tool-item:hover {
  background-color: #f3f4f6;
}

/* Responsive design */
@media (min-width: 640px) {
  .attribution-container {
    padding-top: 5rem; /* Increased padding for medium screens */
    padding-bottom: 3rem;
  }

  .tool-item {
    padding: 1rem;
  }

  .software-card,
  .resource-card {
    padding: 1.5rem;
  }
}

@media (min-width: 768px) {
  .attribution-container {
    padding-top: 6rem; /* Increased padding for large screens */
    padding-bottom: 4rem;
  }

  .tool-item {
    padding: 1.25rem;
  }

  .software-card,
  .resource-card {
    padding: 1.75rem;
  }
}

/* Enhanced focus states */
a:focus-visible,
button:focus-visible {
  outline: 2px solid var(--color-primary-400);
  outline-offset: 2px;
}

/* Smooth transitions for sections */
section {
  transition: all 0.3s ease-out;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Attribution Form Styles */
.iframe-container {
  position: relative;
  background-color: #f9fafb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.iframe-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, var(--color-primary-400), var(--color-secondary-400));
}

#igem-attribution-form {
  background-color: white;
  transition: opacity 0.3s ease-in-out;
}

/* Loading state for iframe */
.iframe-container.loading::after {
  content: 'Loading attribution form...';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--color-primary-600);
  font-weight: 500;
}

/* Responsive iframe heights */
@media (max-width: 639px) {
  #igem-attribution-form {
    min-height: 600px;
  }
}

@media (min-width: 640px) and (max-width: 767px) {
  #igem-attribution-form {
    min-height: 700px;
  }
}

@media (min-width: 768px) {
  #igem-attribution-form {
    min-height: 800px;
  }
}
</style>
