<template>
  <div class="overflow-hidden">
    <!-- Modern 404 Section matching Homepage Style -->
    <section
      class="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-white to-primary-50/20 overflow-hidden"
    >
      <!-- 3D Animated Background Elements (matching homepage) -->
      <div class="absolute inset-0 overflow-hidden">
        <!-- Floating 3D orbs with gradient -->
        <div
          class="absolute top-20 left-10 w-72 h-72 bg-gradient-to-br from-primary-400/20 to-secondary-400/20 rounded-full blur-3xl animate-float"
        ></div>
        <div
          class="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-br from-secondary-400/20 to-primary-400/20 rounded-full blur-3xl animate-float-delayed"
        ></div>
        <div
          class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[40rem] h-[40rem] bg-gradient-to-br from-primary-300/10 to-secondary-300/10 rounded-full blur-3xl"
        ></div>

        <!-- Grid pattern overlay -->
        <div class="absolute inset-0 bg-grid-pattern opacity-[0.02]"></div>
      </div>

      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 relative z-10">
        <!-- Main 404 Content with Modern Typography -->
        <div class="text-center">
          <!-- 404 Number Display -->
          <div class="mb-8" data-aos="fade-up">
            <h1
              class="text-[10rem] sm:text-[12rem] md:text-[14rem] lg:text-[16rem] font-black leading-none"
            >
              <span
                class="text-transparent bg-clip-text bg-gradient-to-r from-primary-600 via-primary-500 to-secondary-600 animate-gradient"
              >
                404
              </span>
            </h1>
          </div>

          <!-- Modern Badge -->
          <div class="mb-6" data-aos="fade-up" data-aos-delay="100">
            <span
              class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-red-100/50 text-red-700 backdrop-blur-sm border border-red-200/30"
            >
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                />
              </svg>
              Page Not Found
            </span>
          </div>

          <!-- Bold Title with Better Hierarchy -->
          <h2
            class="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 text-slate-900"
            data-aos="fade-up"
            data-aos-delay="200"
          >
            Oops! Lost in the Lab
          </h2>

          <!-- Minimal Subtitle -->
          <p
            class="max-w-2xl mx-auto text-lg sm:text-xl text-slate-600 mb-12 font-light leading-relaxed"
            data-aos="fade-up"
            data-aos-delay="300"
          >
            The page you're looking for seems to have wandered off into the molecular void. Let's
            get you back on track.
          </p>

          <!-- Modern CTA Buttons (matching homepage style) -->
          <div
            class="flex flex-col sm:flex-row gap-4 justify-center items-center"
            data-aos="fade-up"
            data-aos-delay="400"
          >
            <router-link
              to="/"
              class="group inline-flex items-center justify-center px-8 py-4 text-base font-medium rounded-xl text-white bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 12l2-2m0 0l7-7 7 7M5 10v10a2 2 0 002 2h10a2 2 0 002-2V10"
                />
              </svg>
              <span>Back to Home</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 ml-2 group-hover:translate-x-1 transition-transform"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </router-link>

            <button
              @click="goBack"
              class="group inline-flex items-center justify-center px-8 py-4 text-base font-medium rounded-xl text-slate-700 bg-white border-2 border-slate-200 hover:border-primary-300 hover:bg-primary-50 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-300"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M10 19l-7-7m0 0l7-7m-7 7h18"
                />
              </svg>
              <span>Go Back</span>
            </button>
          </div>

          <!-- Quick Links Section with Modern Cards -->
          <div class="mt-20" data-aos="fade-up" data-aos-delay="500">
            <p class="text-sm font-medium text-slate-500 mb-6">Popular destinations</p>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 max-w-3xl mx-auto">
              <router-link
                to="/project/overview"
                class="group bg-white/50 backdrop-blur-sm border border-slate-200/30 rounded-xl p-4 hover:bg-white/60 hover:border-primary-300 transition-all duration-300"
              >
                <div class="flex items-center justify-center mb-2">
                  <div
                    class="w-10 h-10 bg-primary-100/50 rounded-lg flex items-center justify-center group-hover:bg-primary-100/70 transition-colors"
                  >
                    <svg
                      class="w-5 h-5 text-primary-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                </div>
                <h3
                  class="font-semibold text-slate-800 group-hover:text-primary-700 transition-colors"
                >
                  Our Project
                </h3>
                <p class="text-sm text-slate-600 mt-1">Learn about SnaPFAS</p>
              </router-link>

              <router-link
                to="/wet-lab/experiment"
                class="group bg-white/50 backdrop-blur-sm border border-slate-200/30 rounded-xl p-4 hover:bg-white/60 hover:border-secondary-300 transition-all duration-300"
              >
                <div class="flex items-center justify-center mb-2">
                  <div
                    class="w-10 h-10 bg-secondary-100/50 rounded-lg flex items-center justify-center group-hover:bg-secondary-100/70 transition-colors"
                  >
                    <svg
                      class="w-5 h-5 text-secondary-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"
                      />
                    </svg>
                  </div>
                </div>
                <h3
                  class="font-semibold text-slate-800 group-hover:text-secondary-700 transition-colors"
                >
                  Lab Work
                </h3>
                <p class="text-sm text-slate-600 mt-1">Explore our experiments</p>
              </router-link>

              <router-link
                to="/teammembers"
                class="group bg-white/50 backdrop-blur-sm border border-slate-200/30 rounded-xl p-4 hover:bg-white/60 hover:border-purple-300 transition-all duration-300"
              >
                <div class="flex items-center justify-center mb-2">
                  <div
                    class="w-10 h-10 bg-purple-100/50 rounded-lg flex items-center justify-center group-hover:bg-purple-100/70 transition-colors"
                  >
                    <svg
                      class="w-5 h-5 text-purple-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                      />
                    </svg>
                  </div>
                </div>
                <h3
                  class="font-semibold text-slate-800 group-hover:text-purple-700 transition-colors"
                >
                  Our Team
                </h3>
                <p class="text-sm text-slate-600 mt-1">Meet the researchers</p>
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

// Go back to previous page function
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}
</script>

<style scoped>
/* Grid pattern background */
.bg-grid-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Floating animation for background orbs */
@keyframes float {
  0% {
    transform: translate(0, 0) rotate(0deg);
  }
  33% {
    transform: translate(30px, -30px) rotate(120deg);
  }
  66% {
    transform: translate(-20px, 20px) rotate(240deg);
  }
  100% {
    transform: translate(0, 0) rotate(360deg);
  }
}

.animate-float {
  animation: float 20s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float 20s ease-in-out infinite;
  animation-delay: -5s;
}

/* Gradient text animation */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

/* Modern button hover effects */
.group:hover .group-hover\:translate-x-1 {
  transform: translateX(0.25rem);
}

.group:hover .group-hover\:-translate-x-1 {
  transform: translateX(-0.25rem);
}

/* Smooth transitions */
* {
  transition-property:
    color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow,
    transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .text-\[10rem\] {
    font-size: 8rem;
  }
}

/* Focus states for accessibility */
a:focus-visible,
button:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* Card hover effects */
.group:hover {
  transform: translateY(-2px);
}

/* Loading states */
[data-aos] {
  opacity: 0;
  transition-property: opacity, transform;
}

[data-aos].aos-animate {
  opacity: 1;
}

/* Ensure proper spacing on all screen sizes */
section {
  min-height: calc(100vh - 64px);
}

/* Backdrop filter support fallback */
@supports not (backdrop-filter: blur(12px)) {
  .backdrop-blur-sm {
    background-color: rgba(255, 255, 255, 0.9);
  }
}
</style>
