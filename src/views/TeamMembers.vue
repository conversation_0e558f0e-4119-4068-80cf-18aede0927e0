<template>
  <div class="team-container pt-8">
    <div class="max-w-3xl mx-auto mb-8 sm:mb-12 md:mb-16 px-4">
      <h1 class="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6 text-center text-gray-900">
        Team Members
      </h1>
      <div
        class="w-20 h-1 bg-gradient-to-r from-primary-400 to-secondary-400 mx-auto mb-6 rounded-full"
      ></div>
      <p class="text-base sm:text-lg md:text-xl text-gray-600 text-center leading-relaxed">
        Meet our dedicated team members who worked together to make this project possible.
      </p>
    </div>

    <!-- Team Photo Section -->
    <section class="max-w-7xl mx-auto px-4 sm:px-6 mb-8 sm:mb-12 md:mb-16">
      <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
        <div class="p-6 sm:p-8 md:p-10">
          <h2
            class="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 sm:mb-6 text-center text-gray-900"
          >
            Our Team
          </h2>
          <div class="w-full rounded-xl overflow-hidden shadow-md">
            <img
              src="https://static.igem.wiki/teams/5610/wiki/teamphoto/team/team.webp"
              alt="BASIS China iGEM Team 2025"
              class="w-full h-auto object-cover"
              loading="eager"
            />
          </div>
          <p class="mt-4 sm:mt-6 text-center text-gray-600 text-base sm:text-lg">
            BASIS China iGEM Team 2025 - United in our mission to tackle PFAS contamination
          </p>
        </div>
      </div>
    </section>

    <!-- Mobile Table of Contents -->
    <div class="block md:hidden max-w-7xl mx-auto px-4 mb-6">
      <ArticleTableOfContents :items="tocItems" title="Contents" :default-open="false" />
    </div>

    <div class="flex flex-col md:flex-row max-w-7xl mx-auto px-4 sm:px-6">
      <!-- Desktop Table of Contents -->
      <div class="hidden md:block w-64 h-screen sticky top-0 overflow-y-auto p-4">
        <ArticleTableOfContents :items="tocItems" title="Contents" />
      </div>

      <!-- Main Content -->
      <div class="flex-1 md:pl-8 bg-gray-50 min-h-screen">
        <article class="max-w-4xl">
          <!-- Team Leaders Section -->
          <section
            id="team-leaders"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Team Leaders
            </h2>
            <div class="pl-4">
              <div class="scrollable-container">
                <div
                  v-for="(leader, index) in leaders"
                  :key="'leader-' + index"
                  class="member-card group"
                >
                  <div
                    class="aspect-w-1 aspect-h-1 mb-4 bg-gradient-to-br from-gray-100 to-gray-50 rounded-xl overflow-visible shadow-md group-hover:shadow-lg transition-shadow duration-300"
                  >
                    <div
                      v-if="!leader.photo"
                      class="w-full h-full flex items-center justify-center text-gray-400 photo-placeholder"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-20 w-20"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                          clip-rule="evenodd"
                        />
                      </svg>
                    </div>
                    <img
                      v-else
                      :src="leader.photo"
                      :alt="leader.name"
                      class="w-full h-full object-cover"
                    />
                  </div>
                  <h3
                    class="text-xl font-semibold text-gray-900 group-hover:text-primary-700 transition-colors duration-200"
                  >
                    {{ leader.name }}
                  </h3>
                  <p class="text-base text-gray-600 font-medium">{{ leader.role }}</p>
                  <p v-if="leader.bio" class="mt-2 text-sm text-gray-500 line-clamp-3">
                    {{ leader.bio }}
                  </p>
                </div>
              </div>
            </div>
          </section>

          <!-- Team Members Section -->
          <section
            id="team-members"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Team Members
            </h2>
            <div class="pl-4">
              <!-- Iterate through all groups and members without a group -->
              <div
                v-for="(group, groupIndex) in memberGroups"
                :key="'group-' + groupIndex"
                class="mb-10"
              >
                <!-- Group title -->
                <div v-if="group.name" class="mb-6 border-b-2 border-primary-100 pb-3">
                  <h3 class="text-xl font-semibold text-primary-700 flex items-center gap-2">
                    <span class="w-1 h-6 bg-primary-500 rounded-full"></span>
                    {{ group.name }}
                  </h3>
                </div>

                <!-- Group members list -->
                <div class="scrollable-container">
                  <div
                    v-for="(member, index) in group.members"
                    :key="'member-' + groupIndex + '-' + index"
                    class="member-card group"
                  >
                    <div
                      class="aspect-w-1 aspect-h-1 mb-4 bg-gradient-to-br from-gray-100 to-gray-50 rounded-xl overflow-visible shadow-md group-hover:shadow-lg transition-shadow duration-300"
                    >
                      <div
                        v-if="!member.photo"
                        class="w-full h-full flex items-center justify-center text-gray-400 photo-placeholder"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-20 w-20"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </div>
                      <img
                        v-else
                        :src="member.photo"
                        :alt="member.name"
                        class="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <h3
                        class="text-lg font-semibold text-gray-900 group-hover:text-primary-700 transition-colors duration-200"
                      >
                        {{ member.name }}
                      </h3>
                      <p class="text-base text-gray-600">{{ member.role }}</p>
                      <p v-if="member.bio" class="mt-2 text-sm text-gray-500 line-clamp-3">
                        {{ member.bio }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- Advisors Section -->
          <section
            id="advisors"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Advisors
            </h2>
            <div class="pl-4">
              <div class="scrollable-container">
                <div
                  v-for="(advisor, index) in advisors"
                  :key="'advisor-' + index"
                  class="member-card group"
                >
                  <div
                    class="aspect-w-1 aspect-h-1 mb-4 bg-gradient-to-br from-gray-100 to-gray-50 rounded-xl overflow-visible shadow-md group-hover:shadow-lg transition-shadow duration-300"
                  >
                    <div
                      v-if="!advisor.photo"
                      class="w-full h-full flex items-center justify-center text-gray-400 photo-placeholder"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-20 w-20"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                          clip-rule="evenodd"
                        />
                      </svg>
                    </div>
                    <img
                      v-else
                      :src="advisor.photo"
                      :alt="advisor.name"
                      class="w-full h-full object-cover"
                    />
                  </div>
                  <h3
                    class="text-lg font-semibold text-gray-900 group-hover:text-primary-700 transition-colors duration-200"
                  >
                    {{ advisor.name }}
                  </h3>
                  <p class="text-base text-gray-600">{{ advisor.role }}</p>
                  <p v-if="advisor.bio" class="mt-2 text-sm text-gray-500 line-clamp-3">
                    {{ advisor.bio }}
                  </p>
                </div>
              </div>
            </div>
          </section>

          <!-- PIs Section -->
          <section
            id="principal-investigators"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Principal Investigators
            </h2>
            <div class="pl-4">
              <div class="scrollable-container">
                <div v-for="(pi, index) in pis" :key="'pi-' + index" class="member-card group">
                  <div
                    class="aspect-w-1 aspect-h-1 mb-4 bg-gradient-to-br from-gray-100 to-gray-50 rounded-xl overflow-visible shadow-md group-hover:shadow-lg transition-shadow duration-300"
                  >
                    <div
                      v-if="!pi.photo"
                      class="w-full h-full flex items-center justify-center text-gray-400 photo-placeholder"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-20 w-20"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                          clip-rule="evenodd"
                        />
                      </svg>
                    </div>
                    <img v-else :src="pi.photo" :alt="pi.name" class="w-full h-full object-cover" />
                  </div>
                  <h3
                    class="text-xl font-semibold text-gray-900 group-hover:text-primary-700 transition-colors duration-200"
                  >
                    {{ pi.name }}
                  </h3>
                  <p class="text-base text-gray-600 font-medium">{{ pi.piRole }}</p>
                  <p v-if="pi.bio" class="mt-2 text-sm text-gray-500 line-clamp-3">{{ pi.bio }}</p>
                </div>
              </div>
            </div>
          </section>

          <!-- Instructors Section -->
          <section
            id="instructors"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Instructors
            </h2>
            <div class="pl-4">
              <div class="scrollable-container">
                <div
                  v-for="(instructor, index) in instructors"
                  :key="'instructor-' + index"
                  class="member-card group"
                >
                  <div
                    class="aspect-w-1 aspect-h-1 mb-4 bg-gradient-to-br from-gray-100 to-gray-50 rounded-xl overflow-visible shadow-md group-hover:shadow-lg transition-shadow duration-300"
                  >
                    <div
                      v-if="!instructor.photo"
                      class="w-full h-full flex items-center justify-center text-gray-400 photo-placeholder"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-20 w-20"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                          clip-rule="evenodd"
                        />
                      </svg>
                    </div>
                    <img
                      v-else
                      :src="instructor.photo"
                      :alt="instructor.name"
                      class="w-full h-full object-cover"
                    />
                  </div>
                  <h3
                    class="text-xl font-semibold text-gray-900 group-hover:text-primary-700 transition-colors duration-200"
                  >
                    {{ instructor.name }}
                  </h3>
                  <p class="text-base text-gray-600 font-medium">{{ instructor.role }}</p>
                  <p v-if="instructor.bio" class="mt-2 text-sm text-gray-500 line-clamp-3">
                    {{ instructor.bio }}
                  </p>
                </div>
              </div>
            </div>
          </section>
        </article>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import ArticleTableOfContents from '@/components/ArticleTableOfContents.vue'
import teamData, { TeamGroup } from '@/data/teamMembers'
import AOS from 'aos'
import 'aos/dist/aos.css'
import { computed, onMounted, ref } from 'vue'

const { pis, instructors, leaders, members, advisors } = teamData

// Table of contents items
const tocItems = ref([
  { title: 'Team Leaders', url: '#team-leaders', depth: 1 },
  { title: 'Team Members', url: '#team-members', depth: 1 },
  { title: 'Advisors', url: '#advisors', depth: 1 },
  { title: 'Principal Investigators', url: '#principal-investigators', depth: 1 },
  { title: 'Instructors', url: '#instructors', depth: 1 },
])

// Group members based on TeamGroup enum
const memberGroups = computed(() => {
  // Create groups array
  const groups = []

  // Get all TeamGroup enum values as grouping basis
  const allGroups = [TeamGroup.WET, TeamGroup.DRY_LAB, TeamGroup.DESIGN_ENT, TeamGroup.HP_PROMOTION]

  // Create a group for each team group
  for (const groupName of allGroups) {
    // Find all members of this group
    const groupMembers = members.filter(m => m.group === groupName)

    if (groupMembers.length > 0) {
      // Put group leaders at the top
      // Sorting logic removed
      groups.push({
        name: groupName,
        members: groupMembers,
      })
    }
  }

  // Add members with no group assigned
  const ungroupedMembers = members.filter(m => !m.group)
  if (ungroupedMembers.length > 0) {
    groups.push({
      name: 'Other Members',
      members: ungroupedMembers,
    })
  }

  return groups
})

onMounted(() => {
  AOS.init({
    duration: 800,
    easing: 'ease-out',
    once: true,
    offset: 50,
  })
})
</script>

<style scoped>
.team-container {
  min-height: calc(100vh - 64px); /* Adjust based on your header height */
  background-color: rgb(249, 250, 251); /* bg-gray-50 */
  padding-top: 4rem; /* Increased top padding to avoid overlap with navigation */
  padding-bottom: 2rem;
}

.scrollable-container {
  display: flex;
  overflow-x: auto;
  padding: 1.5rem 1rem;
  gap: 1.5rem;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.3) rgba(0, 0, 0, 0.1);
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

.member-card {
  padding: 1.25rem;
  border-radius: 1rem;
  border: 2px solid transparent;
  background-color: rgb(249, 250, 251);
  flex-shrink: 0;
  min-width: 300px;
  max-width: 340px;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.member-card:hover {
  border-color: var(--color-primary-200);
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  z-index: 30;
}

/* Ensure all avatar containers have the same dimensions */
.member-card .aspect-w-1,
.scrollable-container .aspect-w-1 {
  width: 260px;
  height: 0;
  margin: 0 auto;
  padding-bottom: 260px;
}

/* Base avatar container styles */
.aspect-w-1 {
  position: relative;
  padding-bottom: 100%;
  width: 100%;
  max-width: 280px;
  height: 0;
}

.aspect-h-1 > * {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  object-fit: cover;
}

/* Icon and title alignment fixes */
.section-header {
  display: flex;
  align-items: center;
}

/* Standardize SVG default avatar styles */
.photo-placeholder {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
}

.photo-placeholder svg {
  width: 100px !important;
  height: 100px !important;
  opacity: 0.4;
  transition: all 0.3s ease;
}

.member-card:hover .photo-placeholder svg {
  transform: scale(1.1);
  opacity: 0.5;
}

/* Line clamp utility */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Enhanced focus states */
.member-card:focus-visible {
  outline: 2px solid var(--color-primary-400);
  outline-offset: 2px;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Responsive design */
@media (min-width: 640px) {
  .team-container {
    padding-top: 5rem; /* Increased padding for medium screens */
    padding-bottom: 3rem;
  }

  .member-card {
    padding: 1rem;
    min-width: 280px;
  }

  /* Ensure text is readable on small screens */
  h3 {
    font-size: 1.125rem;
  }

  p {
    font-size: 1rem;
  }

  .scrollable-container {
    gap: 1rem;
  }
}

@media (min-width: 768px) {
  .team-container {
    padding-top: 6rem; /* Increased padding for large screens */
    padding-bottom: 4rem;
  }

  .scrollable-container {
    gap: 2.5rem;
  }

  .member-card {
    padding: 1.5rem;
  }
}

/* Custom scrollbar styles for webkit browsers */
.scrollable-container::-webkit-scrollbar {
  height: 10px;
}

.scrollable-container::-webkit-scrollbar-track {
  background: var(--color-gray-100);
  border-radius: 10px;
}

.scrollable-container::-webkit-scrollbar-thumb {
  background: var(--color-primary-400);
  border-radius: 10px;
  border: 2px solid var(--color-gray-100);
}

.scrollable-container::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary-500);
}

/* Firefox scrollbar styles */
.scrollable-container {
  scrollbar-width: thin;
  scrollbar-color: var(--color-primary-400) var(--color-gray-100);
}

/* Loading animation for images */
@keyframes image-loading {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.member-card img {
  animation: image-loading 0.3s ease-out;
}
</style>
