<template>
  <div class="hardware-development-container pt-8">
    <div class="max-w-3xl mx-auto mb-8 sm:mb-12 md:mb-16 px-4">
      <h1 class="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6 text-center text-gray-900">
        Hardware Development
      </h1>
      <div
        class="w-20 h-1 bg-gradient-to-r from-primary-400 to-secondary-400 mx-auto mb-6 rounded-full"
      ></div>
      <p class="text-base sm:text-lg md:text-xl text-gray-600 text-center leading-relaxed">
        Engineering hardware solutions to support our synthetic biology applications.
      </p>
    </div>

    <!-- Mobile Table of Contents -->
    <div class="block md:hidden max-w-7xl mx-auto px-4 mb-6">
      <ArticleTableOfContents :items="tocItems" title="Contents" :default-open="false" />
    </div>

    <div class="flex flex-col md:flex-row max-w-7xl mx-auto px-4 sm:px-6">
      <!-- Desktop Table of Contents -->
      <div class="hidden md:block w-64 h-screen sticky top-0 overflow-y-auto p-4">
        <ArticleTableOfContents :items="tocItems" title="Contents" />
      </div>

      <!-- Main Content -->
      <div class="flex-1 md:pl-8 bg-gray-50 min-h-screen">
        <article class="max-w-4xl space-y-8 sm:space-y-10 md:space-y-12">
          <section
            id="overview"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Overview
            </h2>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              Our hardware platform integrates various components to create a robust and efficient
              biosensor detection system.
            </p>
          </section>

          <section
            id="system-architecture"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              System Architecture
            </h2>
            <div class="space-y-6 sm:space-y-8 pl-4">
              <h3
                id="sensor-module"
                class="text-xl sm:text-xl md:text-2xl font-semibold mb-2 sm:mb-3 text-primary-600"
              >
                Sensor Module
              </h3>
              <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6">
                The sensor module consists of:
              </p>
              <ul
                class="list-disc pl-6 space-y-1 sm:space-y-2 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6"
              >
                <li>Optical detection system</li>
                <li>Temperature control unit</li>
                <li>Sample handling mechanism</li>
                <li>Environmental sensors</li>
              </ul>

              <h3
                id="control-system"
                class="text-xl sm:text-xl md:text-2xl font-semibold mb-2 sm:mb-3 text-primary-600"
              >
                Control System
              </h3>
              <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6">
                Key components of the control system:
              </p>
              <ul
                class="list-disc pl-6 space-y-1 sm:space-y-2 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6"
              >
                <li>Microcontroller unit</li>
                <li>Power management system</li>
                <li>Communication interfaces</li>
                <li>User interface display</li>
              </ul>
            </div>
          </section>

          <section
            id="performance"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Performance Specifications
            </h2>
            <div class="space-y-6 sm:space-y-8 pl-4">
              <h3
                id="electrical"
                class="text-xl sm:text-xl md:text-2xl font-semibold mb-2 sm:mb-3 text-primary-600"
              >
                Electrical Characteristics
              </h3>
              <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6">
                Power consumption analysis:
              </p>
              <div class="bg-gray-50 p-4 sm:p-6 rounded-lg shadow-sm mb-4 sm:mb-6 overflow-x-auto">
                <KatexMath
                  expression="P_{total} = V_{cc} \times (I_{sensor} + I_{mcu} + I_{display})"
                  :display="true"
                  class="text-xl"
                />
              </div>

              <h3
                id="thermal"
                class="text-xl sm:text-xl md:text-2xl font-semibold mb-2 sm:mb-3 text-primary-600"
              >
                Thermal Management
              </h3>
              <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6">
                Temperature control efficiency:
              </p>
              <div class="bg-gray-50 p-4 sm:p-6 rounded-lg shadow-sm mb-4 sm:mb-6 overflow-x-auto">
                <KatexMath
                  expression="\eta_{thermal} = \frac{Q_{useful}}{Q_{total}} \times 100\%"
                  :display="true"
                  class="text-xl"
                />
              </div>
            </div>
          </section>

          <section
            id="validation"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Hardware Validation
            </h2>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              Our hardware validation process includes:
            </p>
            <ul
              class="list-disc pl-6 space-y-1 sm:space-y-2 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6 ml-4"
            >
              <li>Functional testing</li>
              <li>Environmental stress testing</li>
              <li>Reliability assessment</li>
              <li>Safety certification</li>
            </ul>
          </section>

          <section
            id="future-improvements"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Future Improvements
            </h2>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              Planned hardware improvements include:
            </p>
            <ul
              class="list-disc pl-6 space-y-1 sm:space-y-2 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6 ml-4"
            >
              <li>Miniaturization of components</li>
              <li>Enhanced power efficiency</li>
              <li>Wireless connectivity options</li>
              <li>Advanced user interface features</li>
            </ul>
          </section>
        </article>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import KatexMath from '@/components/KatexMath.vue'
import ArticleTableOfContents from '@/components/ArticleTableOfContents.vue'

// Using the standard TOC item structure
const tocItems = ref([
  { title: 'Overview', url: '#overview', depth: 1 },
  { title: 'System Architecture', url: '#system-architecture', depth: 1 },
  { title: 'Sensor Module', url: '#sensor-module', depth: 2 },
  { title: 'Control System', url: '#control-system', depth: 2 },
  { title: 'Performance Specifications', url: '#performance', depth: 1 },
  { title: 'Electrical Characteristics', url: '#electrical', depth: 2 },
  { title: 'Thermal Management', url: '#thermal', depth: 2 },
  { title: 'Hardware Validation', url: '#validation', depth: 1 },
  { title: 'Future Improvements', url: '#future-improvements', depth: 1 },
])
</script>

<style scoped>
.hardware-development-container {
  min-height: calc(100vh - 64px); /* Adjust based on your header height */
  background-color: rgb(249, 250, 251); /* bg-gray-50 */
  padding-top: 4rem; /* Increase top spacing to solve issue with navigation bar being too close */
  padding-bottom: 2rem;
}

@media (min-width: 640px) {
  .hardware-development-container {
    padding-top: 5rem; /* Increase top spacing for medium screens */
    padding-bottom: 3rem;
  }
}

@media (min-width: 768px) {
  .hardware-development-container {
    padding-top: 6rem; /* Increase top spacing for large screens */
    padding-bottom: 4rem;
  }
}
</style>
