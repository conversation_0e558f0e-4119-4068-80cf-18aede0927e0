<template>
  <div class="software-development-container pt-8">
    <div class="max-w-3xl mx-auto mb-8 sm:mb-12 md:mb-16 px-4">
      <h1 class="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6 text-center text-gray-900">
        Software Development
      </h1>
      <div
        class="w-20 h-1 bg-gradient-to-r from-primary-400 to-secondary-400 mx-auto mb-6 rounded-full"
      ></div>
      <p class="text-base sm:text-lg md:text-xl text-gray-600 text-center leading-relaxed">
        Our software solutions for data analysis, visualization, and system control.
      </p>
    </div>

    <!-- Mobile Table of Contents -->
    <div class="block md:hidden max-w-7xl mx-auto px-4 mb-6">
      <ArticleTableOfContents :items="tocItems" title="Contents" :default-open="false" />
    </div>

    <div class="flex flex-col md:flex-row max-w-7xl mx-auto px-4 sm:px-6">
      <!-- Desktop Table of Contents -->
      <div class="hidden md:block w-64 h-screen sticky top-0 overflow-y-auto p-4">
        <ArticleTableOfContents :items="tocItems" title="Contents" />
      </div>

      <!-- Main Content -->
      <div class="flex-1 md:pl-8 bg-gray-50 min-h-screen">
        <article class="max-w-4xl space-y-8 sm:space-y-10 md:space-y-12">
          <section
            id="overview"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Overview
            </h2>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              Our software platform provides comprehensive tools for biosensor data analysis,
              visualization, and system control.
            </p>
          </section>

          <section
            id="architecture"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              System Architecture
            </h2>
            <div class="space-y-6 sm:space-y-8 pl-4">
              <h3
                id="backend"
                class="text-xl sm:text-xl md:text-2xl font-semibold mb-2 sm:mb-3 text-primary-600"
              >
                Backend Services
              </h3>
              <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6">
                Key backend components:
              </p>
              <ul
                class="list-disc pl-6 space-y-1 sm:space-y-2 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6"
              >
                <li>Data processing pipeline</li>
                <li>RESTful API endpoints</li>
                <li>Database management</li>
                <li>Authentication system</li>
              </ul>

              <h3
                id="frontend"
                class="text-xl sm:text-xl md:text-2xl font-semibold mb-2 sm:mb-3 text-primary-600"
              >
                Frontend Interface
              </h3>
              <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6">
                User interface features:
              </p>
              <ul
                class="list-disc pl-6 space-y-1 sm:space-y-2 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6"
              >
                <li>Real-time data visualization</li>
                <li>Interactive analysis tools</li>
                <li>Responsive design</li>
                <li>Intuitive controls</li>
              </ul>
            </div>
          </section>

          <section
            id="data-processing"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Data Processing
            </h2>
            <div class="space-y-6 sm:space-y-8 pl-4">
              <h3
                id="signal-processing"
                class="text-xl sm:text-xl md:text-2xl font-semibold mb-2 sm:mb-3 text-primary-600"
              >
                Signal Processing
              </h3>
              <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6">
                Signal processing algorithm:
              </p>
              <div class="bg-gray-50 p-4 sm:p-6 rounded-lg shadow-sm mb-4 sm:mb-6 overflow-x-auto">
                <KatexMath
                  expression="y[n] = \sum_{k=0}^{M-1} h[k]x[n-k]"
                  :display="true"
                  class="text-xl"
                />
              </div>

              <h3
                id="analysis"
                class="text-xl sm:text-xl md:text-2xl font-semibold mb-2 sm:mb-3 text-primary-600"
              >
                Data Analysis
              </h3>
              <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6">
                Statistical analysis methods:
              </p>
              <div class="bg-gray-50 p-4 sm:p-6 rounded-lg shadow-sm mb-4 sm:mb-6 overflow-x-auto">
                <KatexMath
                  expression="\sigma = \sqrt{\frac{1}{N-1}\sum_{i=1}^N (x_i - \bar{x})^2}"
                  :display="true"
                  class="text-xl"
                />
              </div>
            </div>
          </section>

          <section
            id="visualization"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Data Visualization
            </h2>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              Our visualization tools include:
            </p>
            <ul
              class="list-disc pl-6 space-y-1 sm:space-y-2 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6 ml-4"
            >
              <li>Time series plots</li>
              <li>3D surface visualization</li>
              <li>Interactive dashboards</li>
              <li>Custom chart types</li>
            </ul>
          </section>

          <section
            id="deployment"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Deployment
            </h2>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              Our deployment strategy includes:
            </p>
            <ul
              class="list-disc pl-6 space-y-1 sm:space-y-2 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6 ml-4"
            >
              <li>Continuous Integration/Deployment</li>
              <li>Automated testing</li>
              <li>Version control</li>
              <li>Documentation</li>
            </ul>
          </section>
        </article>
      </div>
    </div>
  </div>
</template>

<script setup>
import ArticleTableOfContents from '@/components/ArticleTableOfContents.vue'
import KatexMath from '@/components/KatexMath.vue'
import { ref } from 'vue'

// Define component name to satisfy ESLint multi-word component name rule
defineOptions({
  name: 'SoftwareDevelopment',
})

const _mobileNavOpen = ref(false)

// Using the standard TOC item structure
const tocItems = ref([
  { title: 'Overview', url: '#overview', depth: 1 },
  { title: 'System Architecture', url: '#architecture', depth: 1 },
  { title: 'Backend Services', url: '#backend', depth: 2 },
  { title: 'Frontend Interface', url: '#frontend', depth: 2 },
  { title: 'Data Processing', url: '#data-processing', depth: 1 },
  { title: 'Signal Processing', url: '#signal-processing', depth: 2 },
  { title: 'Data Analysis', url: '#analysis', depth: 2 },
  { title: 'Data Visualization', url: '#visualization', depth: 1 },
  { title: 'Deployment', url: '#deployment', depth: 1 },
])
</script>

<style scoped>
.software-development-container {
  min-height: calc(100vh - 64px); /* Adjust based on your header height */
  background-color: rgb(249, 250, 251); /* bg-gray-50 */
  padding-top: 4rem; /* Increase top spacing to solve issue with navigation bar being too close */
  padding-bottom: 2rem;
}

@media (min-width: 640px) {
  .software-development-container {
    padding-top: 5rem; /* Increase top spacing for medium screens */
    padding-bottom: 3rem;
  }
}

@media (min-width: 768px) {
  .software-development-container {
    padding-top: 6rem; /* Increase top spacing for large screens */
    padding-bottom: 4rem;
  }
}
</style>
