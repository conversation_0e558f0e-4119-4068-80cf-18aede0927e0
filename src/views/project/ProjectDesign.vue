<template>
  <div class="project-design-container pt-8">
    <div class="max-w-3xl mx-auto mb-8 sm:mb-12 md:mb-16 px-4">
      <h1 class="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6 text-center text-gray-900">
        Project Design
      </h1>
      <div
        class="w-20 h-1 bg-gradient-to-r from-primary-400 to-secondary-400 mx-auto mb-6 rounded-full"
      ></div>
      <p class="text-base sm:text-lg md:text-xl text-gray-600 text-center leading-relaxed">
        Our project design process and methodology for synthetic biology applications.
      </p>
    </div>

    <!-- Mobile Table of Contents -->
    <div class="block md:hidden max-w-7xl mx-auto px-4 mb-6">
      <ArticleTableOfContents :items="tocItems" title="Contents" :default-open="false" />
    </div>

    <div class="flex flex-col md:flex-row max-w-7xl mx-auto px-4 sm:px-6">
      <!-- Desktop Table of Contents -->
      <div class="hidden md:block w-64 h-screen sticky top-0 overflow-y-auto p-4">
        <ArticleTableOfContents :items="tocItems" title="Contents" />
      </div>

      <!-- Main Content -->
      <div class="flex-1 md:pl-8 bg-gray-50 min-h-screen">
        <article class="max-w-4xl space-y-8 sm:space-y-10 md:space-y-12">
          <section
            id="overview"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Project Overview
            </h2>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              Our project focuses on developing a novel biosensor system for detecting environmental
              pollutants. The design incorporates synthetic biology principles and advanced
              molecular engineering techniques.
            </p>
          </section>

          <section
            id="mathematical-model"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Mathematical Model
            </h2>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              The kinetics of our biosensor can be described by the following differential equation:
            </p>
            <div
              class="bg-gray-50 p-4 sm:p-6 rounded-lg shadow-sm mb-4 sm:mb-6 overflow-x-auto ml-4"
            >
              <KatexMath
                expression="\frac{dP}{dt} = k_p[E][S] - k_d[P]"
                :display="true"
                class="text-xl"
              />
            </div>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              Where:
            </p>
            <ul
              class="list-disc pl-6 space-y-1 sm:space-y-2 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6 ml-4"
            >
              <li>
                <KatexMath expression="P" />
                is the product concentration
              </li>
              <li>
                <KatexMath expression="E" />
                is the enzyme concentration
              </li>
              <li>
                <KatexMath expression="S" />
                is the substrate concentration
              </li>
              <li>
                <KatexMath expression="k_p" />
                is the production rate constant
              </li>
              <li>
                <KatexMath expression="k_d" />
                is the degradation rate constant
              </li>
            </ul>
          </section>

          <section
            id="design-principles"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Design Principles
            </h2>
            <div class="space-y-6 sm:space-y-8 pl-4">
              <h3
                id="modularity"
                class="text-xl sm:text-xl md:text-2xl font-semibold mb-2 sm:mb-3 text-primary-600"
              >
                Modularity
              </h3>
              <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6">
                Our design follows a modular approach, allowing for easy component replacement and
                system optimization. The key modules include:
              </p>
              <ul
                class="list-disc pl-6 space-y-1 sm:space-y-2 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6"
              >
                <li>Sensing module</li>
                <li>Processing module</li>
                <li>Output module</li>
              </ul>

              <h3
                id="optimization"
                class="text-xl sm:text-xl md:text-2xl font-semibold mb-2 sm:mb-3 text-primary-600"
              >
                Optimization
              </h3>
              <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6">
                The system's performance is optimized through:
              </p>
              <div class="bg-gray-50 p-4 sm:p-6 rounded-lg shadow-sm mb-4 sm:mb-6 overflow-x-auto">
                <KatexMath
                  expression="\max_{x,y} f(x,y) = \frac{k_{cat}[E_t][S]}{K_M + [S]}"
                  :display="true"
                  class="text-xl"
                />
              </div>
            </div>
          </section>

          <section
            id="implementation"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Implementation
            </h2>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              The implementation process involves several key steps:
            </p>
            <ol
              class="list-decimal pl-6 space-y-1 sm:space-y-2 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6 ml-4"
            >
              <li>Gene synthesis and assembly</li>
              <li>Protein expression optimization</li>
              <li>System integration and testing</li>
            </ol>
          </section>
        </article>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import KatexMath from '@/components/KatexMath.vue'
import ArticleTableOfContents from '@/components/ArticleTableOfContents.vue'

// Using the standard TOC item structure
const tocItems = ref([
  { title: 'Project Overview', url: '#overview', depth: 1 },
  { title: 'Mathematical Model', url: '#mathematical-model', depth: 1 },
  { title: 'Design Principles', url: '#design-principles', depth: 1 },
  { title: 'Modularity', url: '#modularity', depth: 2 },
  { title: 'Optimization', url: '#optimization', depth: 2 },
  { title: 'Implementation', url: '#implementation', depth: 1 },
])
</script>

<style scoped>
.project-design-container {
  min-height: calc(100vh - 64px); /* Adjust based on your header height */
  background-color: rgb(249, 250, 251); /* bg-gray-50 */
  padding-top: 4rem; /* Increased top padding to avoid overlap with navigation */
  padding-bottom: 2rem;
}

@media (min-width: 640px) {
  .project-design-container {
    padding-top: 5rem; /* Increased padding for medium screens */
    padding-bottom: 3rem;
  }
}

@media (min-width: 768px) {
  .project-design-container {
    padding-top: 6rem; /* Increased padding for large screens */
    padding-bottom: 4rem;
  }
}
</style>
