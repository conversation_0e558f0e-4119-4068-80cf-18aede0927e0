<template>
  <div class="project-overview-container pt-8">
    <div class="max-w-3xl mx-auto mb-8 sm:mb-12 md:mb-16 px-4">
      <h1 class="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6 text-center text-gray-900">
        Project Overview
      </h1>
      <div
        class="w-20 h-1 bg-gradient-to-r from-primary-400 to-secondary-400 mx-auto mb-6 rounded-full"
      ></div>
      <p class="text-base sm:text-lg md:text-xl text-gray-600 text-center leading-relaxed">
        An overview of our iGEM project, its goals, methodology, and expected impact.
      </p>
    </div>

    <!-- Mobile Table of Contents -->
    <div class="block md:hidden max-w-7xl mx-auto px-4 mb-6">
      <ArticleTableOfContents :items="tocItems" title="Contents" :default-open="false" />
    </div>

    <div class="flex flex-col md:flex-row max-w-7xl mx-auto px-4 sm:px-6">
      <!-- Desktop Table of Contents -->
      <div class="hidden md:block w-64 h-screen sticky top-0 overflow-y-auto p-4">
        <ArticleTableOfContents :items="tocItems" title="Contents" />
      </div>

      <!-- Main Content -->
      <div class="flex-1 md:pl-8 bg-gray-50 min-h-screen">
        <article class="max-w-4xl">
          <section
            id="introduction"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Introduction
            </h2>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              Our iGEM project aims to address [project goal] through innovative synthetic biology
              approaches. This overview provides a comprehensive look at our project's objectives,
              methodology, and expected outcomes.
            </p>
          </section>

          <section
            id="problem-statement"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Problem Statement
            </h2>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              The problem we are addressing is [description of the problem]. This issue affects
              [stakeholders] and has significant implications for [broader impact areas].
            </p>
            <h3
              id="background"
              class="text-xl sm:text-xl md:text-2xl font-semibold mb-2 sm:mb-3 text-gray-800 mt-6 pl-4"
            >
              Background
            </h3>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              Key background information includes:
            </p>
            <ul
              class="list-none pl-8 space-y-2 sm:space-y-3 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6"
            >
              <li class="flex items-start group">
                <span class="text-primary-500 mr-3 mt-1 flex-shrink-0">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
                <span class="group-hover:text-gray-900 transition-colors duration-200">
                  Current state of the field
                </span>
              </li>
              <li class="flex items-start group">
                <span class="text-primary-500 mr-3 mt-1 flex-shrink-0">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
                <span class="group-hover:text-gray-900 transition-colors duration-200">
                  Previous approaches and their limitations
                </span>
              </li>
              <li class="flex items-start group">
                <span class="text-primary-500 mr-3 mt-1 flex-shrink-0">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
                <span class="group-hover:text-gray-900 transition-colors duration-200">
                  Relevant scientific principles
                </span>
              </li>
              <li class="flex items-start group">
                <span class="text-primary-500 mr-3 mt-1 flex-shrink-0">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
                <span class="group-hover:text-gray-900 transition-colors duration-200">
                  Societal context
                </span>
              </li>
            </ul>
          </section>

          <section
            id="our-solution"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Our Solution
            </h2>
            <h3
              id="approach"
              class="text-xl sm:text-xl md:text-2xl font-semibold mb-2 sm:mb-3 text-gray-800 mt-6 pl-4"
            >
              Approach
            </h3>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              Our approach involves:
            </p>
            <ul
              class="list-none pl-8 space-y-2 sm:space-y-3 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6"
            >
              <li class="flex items-start group">
                <span class="text-primary-500 mr-3 mt-1 flex-shrink-0">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
                <span class="group-hover:text-gray-900 transition-colors duration-200">
                  Novel genetic constructs
                </span>
              </li>
              <li class="flex items-start group">
                <span class="text-primary-500 mr-3 mt-1 flex-shrink-0">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
                <span class="group-hover:text-gray-900 transition-colors duration-200">
                  Innovative methodologies
                </span>
              </li>
              <li class="flex items-start group">
                <span class="text-primary-500 mr-3 mt-1 flex-shrink-0">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
                <span class="group-hover:text-gray-900 transition-colors duration-200">
                  Interdisciplinary collaboration
                </span>
              </li>
              <li class="flex items-start group">
                <span class="text-primary-500 mr-3 mt-1 flex-shrink-0">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
                <span class="group-hover:text-gray-900 transition-colors duration-200">
                  Stakeholder engagement
                </span>
              </li>
            </ul>

            <h3
              id="innovation"
              class="text-xl sm:text-xl md:text-2xl font-semibold mb-2 sm:mb-3 text-gray-800 mt-6 pl-4"
            >
              Innovation
            </h3>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              The innovative aspects of our project include:
            </p>
            <ul
              class="list-none pl-8 space-y-2 sm:space-y-3 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6"
            >
              <li class="flex items-start group">
                <span class="text-primary-500 mr-3 mt-1 flex-shrink-0">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
                <span class="group-hover:text-gray-900 transition-colors duration-200">
                  Novel biological mechanisms
                </span>
              </li>
              <li class="flex items-start group">
                <span class="text-primary-500 mr-3 mt-1 flex-shrink-0">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
                <span class="group-hover:text-gray-900 transition-colors duration-200">
                  Unique application of existing technologies
                </span>
              </li>
              <li class="flex items-start group">
                <span class="text-primary-500 mr-3 mt-1 flex-shrink-0">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
                <span class="group-hover:text-gray-900 transition-colors duration-200">
                  Creative problem-solving approaches
                </span>
              </li>
              <li class="flex items-start group">
                <span class="text-primary-500 mr-3 mt-1 flex-shrink-0">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
                <span class="group-hover:text-gray-900 transition-colors duration-200">
                  Integration of diverse perspectives
                </span>
              </li>
            </ul>
          </section>

          <section
            id="methodology"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Methodology
            </h2>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              Our project methodology includes:
            </p>
            <ul
              class="list-none pl-8 space-y-2 sm:space-y-3 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6"
            >
              <li class="flex items-start group">
                <span class="text-primary-500 mr-3 mt-1 flex-shrink-0">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
                <span class="group-hover:text-gray-900 transition-colors duration-200">
                  Experimental design
                </span>
              </li>
              <li class="flex items-start group">
                <span class="text-primary-500 mr-3 mt-1 flex-shrink-0">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
                <span class="group-hover:text-gray-900 transition-colors duration-200">
                  Computational modeling
                </span>
              </li>
              <li class="flex items-start group">
                <span class="text-primary-500 mr-3 mt-1 flex-shrink-0">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
                <span class="group-hover:text-gray-900 transition-colors duration-200">
                  Prototype development
                </span>
              </li>
              <li class="flex items-start group">
                <span class="text-primary-500 mr-3 mt-1 flex-shrink-0">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
                <span class="group-hover:text-gray-900 transition-colors duration-200">
                  Validation testing
                </span>
              </li>
            </ul>
          </section>

          <section
            id="expected-impact"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Expected Impact
            </h2>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              The anticipated impact of our project includes:
            </p>
            <ul
              class="list-none pl-8 space-y-2 sm:space-y-3 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6"
            >
              <li class="flex items-start group">
                <span class="text-primary-500 mr-3 mt-1 flex-shrink-0">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
                <span class="group-hover:text-gray-900 transition-colors duration-200">
                  Scientific advancements
                </span>
              </li>
              <li class="flex items-start group">
                <span class="text-primary-500 mr-3 mt-1 flex-shrink-0">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
                <span class="group-hover:text-gray-900 transition-colors duration-200">
                  Practical applications
                </span>
              </li>
              <li class="flex items-start group">
                <span class="text-primary-500 mr-3 mt-1 flex-shrink-0">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
                <span class="group-hover:text-gray-900 transition-colors duration-200">
                  Educational value
                </span>
              </li>
              <li class="flex items-start group">
                <span class="text-primary-500 mr-3 mt-1 flex-shrink-0">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </span>
                <span class="group-hover:text-gray-900 transition-colors duration-200">
                  Societal benefits
                </span>
              </li>
            </ul>
          </section>
        </article>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ArticleTableOfContents from '@/components/ArticleTableOfContents.vue'

// Using the standard TOC item structure
const tocItems = ref([
  { title: 'Introduction', url: '#introduction', depth: 1 },
  { title: 'Problem Statement', url: '#problem-statement', depth: 1 },
  { title: 'Background', url: '#background', depth: 2 },
  { title: 'Our Solution', url: '#our-solution', depth: 1 },
  { title: 'Approach', url: '#approach', depth: 2 },
  { title: 'Innovation', url: '#innovation', depth: 2 },
  { title: 'Methodology', url: '#methodology', depth: 1 },
  { title: 'Expected Impact', url: '#expected-impact', depth: 1 },
])
</script>

<style scoped>
.project-overview-container {
  min-height: calc(100vh - 64px); /* Adjust based on your header height */
  background-color: rgb(249, 250, 251); /* bg-gray-50 */
  padding-top: 4rem; /* Increased top padding to avoid overlap with navigation */
  padding-bottom: 2rem;
}

@media (min-width: 640px) {
  .project-overview-container {
    padding-top: 5rem; /* Increased padding for medium screens */
    padding-bottom: 3rem;
  }
}

@media (min-width: 768px) {
  .project-overview-container {
    padding-top: 6rem; /* Increased padding for large screens */
    padding-bottom: 4rem;
  }
}

/* Enhanced focus states */
a:focus-visible,
button:focus-visible {
  outline: 2px solid var(--color-primary-400);
  outline-offset: 2px;
}

/* Smooth transitions for sections */
section {
  transition: all 0.3s ease-out;
}

/* List item hover effects */
ul li {
  transition: all 0.2s ease-out;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}
</style>
