<template>
  <div class="experiment-container pt-8">
    <div class="max-w-3xl mx-auto mb-8 sm:mb-12 md:mb-16 px-4">
      <h1 class="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6 text-center text-gray-900">
        Experimental Design
      </h1>
      <div
        class="w-20 h-1 bg-gradient-to-r from-primary-400 to-secondary-400 mx-auto mb-6 rounded-full"
      ></div>
      <p class="text-base sm:text-lg md:text-xl text-gray-600 text-center leading-relaxed">
        Our experimental protocols, methodologies, and approaches for system validation.
      </p>
    </div>

    <!-- Mobile Table of Contents -->
    <div class="block md:hidden max-w-7xl mx-auto px-4 mb-6">
      <ArticleTableOfContents :items="tocItems" title="Contents" :default-open="false" />
    </div>

    <div class="flex flex-col md:flex-row max-w-7xl mx-auto px-4 sm:px-6">
      <!-- Desktop Table of Contents -->
      <div class="hidden md:block w-64 h-screen sticky top-0 overflow-y-auto p-4">
        <ArticleTableOfContents :items="tocItems" title="Contents" />
      </div>

      <!-- Main Content -->
      <div class="flex-1 md:pl-8 bg-gray-50 min-h-screen">
        <article class="max-w-4xl space-y-8 sm:space-y-10 md:space-y-12">
          <section
            id="methods"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Methods
            </h2>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              Our experimental methods are designed to ensure reproducibility and reliability. We
              follow standard protocols and best practices in synthetic biology research.
            </p>
          </section>

          <section
            id="protocols"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Protocols
            </h2>
            <div class="space-y-6 sm:space-y-8 pl-4">
              <h3
                id="dna-assembly"
                class="text-xl sm:text-xl md:text-2xl font-semibold mb-2 sm:mb-3 text-primary-600"
              >
                DNA Assembly
              </h3>
              <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6">
                We used the following protocol for DNA assembly:
              </p>
              <ol
                class="list-decimal pl-6 space-y-1 sm:space-y-2 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6"
              >
                <li>PCR amplification of DNA fragments</li>
                <li>Restriction enzyme digestion</li>
                <li>Ligation of DNA fragments</li>
                <li>Transformation into competent cells</li>
              </ol>

              <h3
                id="protein-expression"
                class="text-xl sm:text-xl md:text-2xl font-semibold mb-2 sm:mb-3 text-primary-600"
              >
                Protein Expression
              </h3>
              <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6">
                The protein expression protocol includes:
              </p>
              <ul
                class="list-disc pl-6 space-y-1 sm:space-y-2 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6"
              >
                <li>Culture growth conditions</li>
                <li>IPTG induction parameters</li>
                <li>Cell harvesting and lysis</li>
                <li>Protein purification steps</li>
              </ul>
            </div>
          </section>

          <section
            id="data-analysis"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Data Analysis
            </h2>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              Our data analysis approach includes:
            </p>
            <div class="bg-gray-50 p-6 rounded-lg shadow-sm mb-4 sm:mb-6 ml-4">
              <KatexMath
                expression="R^2 = 1 - \frac{\sum_{i=1}^{n} (y_i - \hat{y}_i)^2}{\sum_{i=1}^{n} (y_i - \bar{y})^2}"
                :display="true"
                class="text-lg sm:text-xl"
              />
            </div>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              Where R² represents the coefficient of determination for our experimental data.
            </p>
          </section>

          <section
            id="controls"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Controls and Validation
            </h2>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              We implemented several controls to validate our experimental results:
            </p>
            <ul
              class="list-disc pl-6 space-y-1 sm:space-y-2 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6 ml-4"
            >
              <li>Positive controls</li>
              <li>Negative controls</li>
              <li>Internal standards</li>
              <li>Technical replicates</li>
              <li>Biological replicates</li>
            </ul>
          </section>
        </article>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import KatexMath from '@/components/KatexMath.vue'
import ArticleTableOfContents from '@/components/ArticleTableOfContents.vue'

// Using the standard TOC item structure
const tocItems = ref([
  { title: 'Methods', url: '#methods', depth: 1 },
  { title: 'Protocols', url: '#protocols', depth: 1 },
  { title: 'DNA Assembly', url: '#dna-assembly', depth: 2 },
  { title: 'Protein Expression', url: '#protein-expression', depth: 2 },
  { title: 'Data Analysis', url: '#data-analysis', depth: 1 },
  { title: 'Controls and Validation', url: '#controls', depth: 1 },
])
</script>

<style scoped>
.experiment-container {
  min-height: calc(100vh - 64px); /* Adjust based on your header height */
  background-color: rgb(249, 250, 251); /* bg-gray-50 */
  padding-top: 4rem; /* Increase top spacing to solve issue with navigation bar being too close */
  padding-bottom: 2rem;
}

@media (min-width: 640px) {
  .experiment-container {
    padding-top: 5rem; /* Increase top spacing for medium screens */
    padding-bottom: 3rem;
  }
}

@media (min-width: 768px) {
  .experiment-container {
    padding-top: 6rem; /* Increase top spacing for large screens */
    padding-bottom: 4rem;
  }
}
</style>
