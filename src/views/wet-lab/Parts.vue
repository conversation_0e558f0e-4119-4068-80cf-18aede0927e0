<template>
  <div class="parts-container pt-8">
    <div class="max-w-3xl mx-auto mb-8 sm:mb-12 md:mb-16 px-4">
      <h1 class="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6 text-center text-gray-900">
        BioBrick Parts
      </h1>
      <div
        class="w-20 h-1 bg-gradient-to-r from-primary-400 to-secondary-400 mx-auto mb-6 rounded-full"
      ></div>
      <p class="text-base sm:text-lg md:text-xl text-gray-600 text-center leading-relaxed">
        The genetic parts and components used in our synthetic biology project.
      </p>
    </div>

    <!-- Mobile Table of Contents -->
    <div class="block md:hidden max-w-7xl mx-auto px-4 mb-6">
      <ArticleTableOfContents :items="tocItems" title="Contents" :default-open="false" />
    </div>

    <div class="flex flex-col md:flex-row max-w-7xl mx-auto px-4 sm:px-6">
      <!-- Desktop Table of Contents -->
      <div class="hidden md:block w-64 h-screen sticky top-0 overflow-y-auto p-4">
        <ArticleTableOfContents :items="tocItems" title="Contents" />
      </div>

      <!-- Main Content -->
      <div class="flex-1 md:pl-8 bg-gray-50 min-h-screen">
        <article class="max-w-4xl space-y-8 sm:space-y-10 md:space-y-12">
          <section
            id="overview"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Overview
            </h2>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              Our project utilizes various standardized biological parts to create a functional
              biosensor system. Each part has been carefully selected and characterized.
            </p>
          </section>

          <section
            id="promoters"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Promoter Systems
            </h2>
            <div class="space-y-6 sm:space-y-8 pl-4">
              <h3
                id="constitutive"
                class="text-xl sm:text-xl md:text-2xl font-semibold mb-2 sm:mb-3 text-primary-600"
              >
                Constitutive Promoters
              </h3>
              <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6">
                Expression strength of constitutive promoters:
              </p>
              <div class="bg-gray-50 p-4 sm:p-6 rounded-lg shadow-sm mb-4 sm:mb-6 overflow-x-auto">
                <KatexMath
                  expression="P_{strength} = \alpha \cdot [RNA_{polymerase}] \cdot k_{binding}"
                  :display="true"
                  class="text-xl"
                />
              </div>

              <h3
                id="inducible"
                class="text-xl sm:text-xl md:text-2xl font-semibold mb-2 sm:mb-3 text-primary-600"
              >
                Inducible Promoters
              </h3>
              <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6">
                Key characteristics of our inducible promoters:
              </p>
              <ul
                class="list-disc pl-6 space-y-1 sm:space-y-2 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6"
              >
                <li>Tight regulation</li>
                <li>Low basal expression</li>
                <li>High induction ratio</li>
                <li>Rapid response time</li>
              </ul>
            </div>
          </section>

          <section
            id="coding-sequences"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Coding Sequences
            </h2>
            <div class="space-y-6 sm:space-y-8 pl-4">
              <h3
                id="reporters"
                class="text-xl sm:text-xl md:text-2xl font-semibold mb-2 sm:mb-3 text-primary-600"
              >
                Reporter Proteins
              </h3>
              <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6">
                Our reporter system includes:
              </p>
              <ul
                class="list-disc pl-6 space-y-1 sm:space-y-2 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6"
              >
                <li>Fluorescent proteins (GFP, RFP)</li>
                <li>Luminescent proteins</li>
                <li>Colorimetric enzymes</li>
              </ul>

              <h3
                id="functional"
                class="text-xl sm:text-xl md:text-2xl font-semibold mb-2 sm:mb-3 text-primary-600"
              >
                Functional Proteins
              </h3>
              <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6">
                Key functional proteins in our system:
              </p>
              <ul
                class="list-disc pl-6 space-y-1 sm:space-y-2 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6"
              >
                <li>Sensor proteins</li>
                <li>Signal transducers</li>
                <li>Regulatory elements</li>
              </ul>
            </div>
          </section>

          <section
            id="terminators"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Terminators
            </h2>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              Terminator efficiency is calculated as:
            </p>
            <div
              class="bg-gray-50 p-4 sm:p-6 rounded-lg shadow-sm mb-4 sm:mb-6 overflow-x-auto ml-4"
            >
              <KatexMath
                expression="Efficiency = \left(1 - \frac{Readthrough}{Total\,Transcription}\right) \times 100\%"
                :display="true"
                class="text-xl"
              />
            </div>
          </section>
        </article>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import KatexMath from '@/components/KatexMath.vue'
import ArticleTableOfContents from '@/components/ArticleTableOfContents.vue'

// Using the standard TOC item structure
const tocItems = ref([
  { title: 'Overview', url: '#overview', depth: 1 },
  { title: 'Promoter Systems', url: '#promoters', depth: 1 },
  { title: 'Constitutive Promoters', url: '#constitutive', depth: 2 },
  { title: 'Inducible Promoters', url: '#inducible', depth: 2 },
  { title: 'Coding Sequences', url: '#coding-sequences', depth: 1 },
  { title: 'Reporter Proteins', url: '#reporters', depth: 2 },
  { title: 'Functional Proteins', url: '#functional', depth: 2 },
  { title: 'Terminators', url: '#terminators', depth: 1 },
])
</script>

<style scoped>
.parts-container {
  min-height: calc(100vh - 64px); /* Adjust based on your header height */
  background-color: rgb(249, 250, 251); /* bg-gray-50 */
  padding-top: 4rem; /* Increase top spacing to solve issue with navigation bar being too close */
  padding-bottom: 2rem;
}

@media (min-width: 640px) {
  .parts-container {
    padding-top: 5rem; /* Increase top spacing for medium screens */
    padding-bottom: 3rem;
  }
}

@media (min-width: 768px) {
  .parts-container {
    padding-top: 6rem; /* Increase top spacing for large screens */
    padding-bottom: 4rem;
  }
}
</style>
