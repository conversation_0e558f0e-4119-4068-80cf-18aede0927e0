<template>
  <div class="safety-container pt-8">
    <div class="max-w-3xl mx-auto mb-8 sm:mb-12 md:mb-16 px-4">
      <h1 class="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 sm:mb-6 text-center text-gray-900">
        Laboratory Safety
      </h1>
      <div
        class="w-20 h-1 bg-gradient-to-r from-primary-400 to-secondary-400 mx-auto mb-6 rounded-full"
      ></div>
      <p class="text-base sm:text-lg md:text-xl text-gray-600 text-center leading-relaxed">
        Our comprehensive approach to ensuring safety in experimental procedures and biosecurity.
      </p>
    </div>

    <!-- Mobile Table of Contents -->
    <div class="block md:hidden max-w-7xl mx-auto px-4 mb-6">
      <ArticleTableOfContents :items="tocItems" title="Contents" :default-open="false" />
    </div>

    <div class="flex flex-col md:flex-row max-w-7xl mx-auto px-4 sm:px-6">
      <!-- Desktop Table of Contents -->
      <div class="hidden md:block w-64 h-screen sticky top-0 overflow-y-auto p-4">
        <ArticleTableOfContents :items="tocItems" title="Contents" />
      </div>

      <!-- Main Content -->
      <div class="flex-1 md:pl-8 bg-gray-50 min-h-screen">
        <article class="max-w-4xl space-y-8 sm:space-y-10 md:space-y-12">
          <section
            id="overview"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Safety Overview
            </h2>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              Our commitment to safety encompasses all aspects of laboratory work, ensuring the
              protection of personnel, environment, and community.
            </p>
          </section>

          <section
            id="biosafety"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Biosafety Measures
            </h2>
            <div class="space-y-6 sm:space-y-8 pl-4">
              <h3
                id="containment"
                class="text-xl sm:text-xl md:text-2xl font-semibold mb-2 sm:mb-3 text-primary-600"
              >
                Containment Levels
              </h3>
              <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6">
                Our work is conducted at appropriate biosafety levels:
              </p>
              <ul
                class="list-disc pl-6 space-y-1 sm:space-y-2 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6"
              >
                <li>BSL-1 for standard molecular biology</li>
                <li>BSL-2 for work with recombinant organisms</li>
                <li>Additional precautions as needed</li>
              </ul>

              <h3
                id="ppe"
                class="text-xl sm:text-xl md:text-2xl font-semibold mb-2 sm:mb-3 text-primary-600"
              >
                Personal Protective Equipment
              </h3>
              <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6">
                Required PPE for laboratory work:
              </p>
              <ul
                class="list-disc pl-6 space-y-1 sm:space-y-2 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6"
              >
                <li>Laboratory coats</li>
                <li>Safety goggles</li>
                <li>Appropriate gloves</li>
                <li>Closed-toe shoes</li>
              </ul>
            </div>
          </section>

          <section
            id="risk-assessment"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Risk Assessment
            </h2>
            <div class="space-y-6 sm:space-y-8 pl-4">
              <h3
                id="hazard-identification"
                class="text-xl sm:text-xl md:text-2xl font-semibold mb-2 sm:mb-3 text-primary-600"
              >
                Hazard Identification
              </h3>
              <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6">
                We conduct thorough risk assessments using:
              </p>
              <div class="bg-gray-50 p-4 sm:p-6 rounded-lg shadow-sm mb-4 sm:mb-6 overflow-x-auto">
                <KatexMath
                  expression="Risk = Likelihood \times Severity"
                  :display="true"
                  class="text-xl"
                />
              </div>

              <h3
                id="mitigation"
                class="text-xl sm:text-xl md:text-2xl font-semibold mb-2 sm:mb-3 text-primary-600"
              >
                Risk Mitigation
              </h3>
              <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6">
                Our risk mitigation strategy includes:
              </p>
              <ul
                class="list-disc pl-6 space-y-1 sm:space-y-2 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6"
              >
                <li>Engineering controls</li>
                <li>Administrative controls</li>
                <li>PPE requirements</li>
                <li>Emergency procedures</li>
              </ul>
            </div>
          </section>

          <section
            id="training"
            class="mb-8 sm:mb-10 md:mb-12 bg-white rounded-xl p-6 sm:p-8 shadow-sm hover:shadow-md transition-shadow duration-300"
          >
            <h2
              class="text-2xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4 text-gray-900 flex items-center gap-3"
            >
              <span class="w-1 h-8 bg-primary-500 rounded-full"></span>
              Safety Training
            </h2>
            <p class="text-base sm:text-lg text-gray-700 leading-relaxed mb-4 sm:mb-6 pl-4">
              All laboratory personnel must complete:
            </p>
            <ul
              class="list-disc pl-6 space-y-1 sm:space-y-2 text-base sm:text-lg text-gray-700 mb-4 sm:mb-6 ml-4"
            >
              <li>Basic laboratory safety training</li>
              <li>Biosafety training</li>
              <li>Chemical safety training</li>
              <li>Emergency response training</li>
              <li>Annual refresher courses</li>
            </ul>
          </section>
        </article>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import KatexMath from '@/components/KatexMath.vue'
import ArticleTableOfContents from '@/components/ArticleTableOfContents.vue'

// Define component name to satisfy ESLint multi-word component name rule
defineOptions({
  name: 'LaboratorySafety',
})

const tocItems = ref([
  { title: 'Safety Overview', url: '#overview', depth: 1 },
  { title: 'Biosafety Measures', url: '#biosafety', depth: 1 },
  { title: 'Containment Levels', url: '#containment', depth: 2 },
  { title: 'Personal Protective Equipment', url: '#ppe', depth: 2 },
  { title: 'Risk Assessment', url: '#risk-assessment', depth: 1 },
  { title: 'Hazard Identification', url: '#hazard-identification', depth: 2 },
  { title: 'Risk Mitigation', url: '#mitigation', depth: 2 },
  { title: 'Safety Training', url: '#training', depth: 1 },
])
</script>

<style scoped>
.safety-container {
  min-height: calc(100vh - 64px); /* Adjust based on your header height */
  background-color: rgb(249, 250, 251); /* bg-gray-50 */
  padding-top: 4rem; /* Increase top spacing to solve issue with navigation bar being too close */
  padding-bottom: 2rem;
}

@media (min-width: 640px) {
  .safety-container {
    padding-top: 5rem; /* Increase top spacing for medium screens */
    padding-bottom: 3rem;
  }
}

@media (min-width: 768px) {
  .safety-container {
    padding-top: 6rem; /* Increase top spacing for large screens */
    padding-bottom: 4rem;
  }
}
</style>
