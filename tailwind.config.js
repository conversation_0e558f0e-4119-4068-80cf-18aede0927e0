const typography = require('@tailwindcss/typography')

module.exports = {
  content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  theme: {
    screens: {
      xs: '480px',
      sm: '640px',
      md: '768px',
      lg: '1024px',
      xl: '1280px',
      '2xl': '1536px',
    },
    extend: {
      // 使用CSS自定义属性扩展颜色系统
      colors: {
        // 保留原有的颜色定义以保持向后兼容
        primary: {
          50: '#ecfdf5',
          100: '#d1fae5',
          200: '#a7f3d0',
          300: '#6ee7b7',
          400: '#34d399',
          500: '#10b981',
          600: '#059669',
          700: '#047857',
          800: '#065f46',
          900: '#064e3b',
          950: '#022c22',
        },
        secondary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
          950: '#172554',
        },
        tertiary: {
          50: '#f0fdfa',
          100: '#ccfbf1',
          200: '#99f6e4',
          300: '#5eead4',
          400: '#2dd4bf',
          500: '#14b8a6',
          600: '#0d9488',
          700: '#0f766e',
          800: '#115e59',
          900: '#134e4a',
          950: '#042f2e',
        },
        // Hero专用渐变颜色
        hero: {
          light: '#357AA1',
          medium: '#2A4883',
          dark: '#090149',
        },
        // 添加设计令牌颜色映射
        token: {
          primary: 'var(--color-primary)',
          'primary-light': 'var(--color-primary-light)',
          'primary-dark': 'var(--color-primary-dark)',
          secondary: 'var(--color-secondary)',
          'secondary-light': 'var(--color-secondary-light)',
          'secondary-dark': 'var(--color-secondary-dark)',
          tertiary: 'var(--color-tertiary)',
          'tertiary-light': 'var(--color-tertiary-light)',
          'tertiary-dark': 'var(--color-tertiary-dark)',
          success: 'var(--color-success)',
          info: 'var(--color-info)',
          warning: 'var(--color-warning)',
          error: 'var(--color-error)',
          'text-primary': 'var(--color-text-primary)',
          'text-secondary': 'var(--color-text-secondary)',
          'text-tertiary': 'var(--color-text-tertiary)',
          'text-disabled': 'var(--color-text-disabled)',
          bg: 'var(--color-background)',
          'bg-secondary': 'var(--color-background-secondary)',
          'bg-tertiary': 'var(--color-background-tertiary)',
          border: 'var(--color-border)',
          'border-hover': 'var(--color-border-hover)',
          'border-focus': 'var(--color-border-focus)',
        },
      },
      // 使用设计令牌扩展间距
      spacing: {
        'space-0': 'var(--space-0)',
        'space-1': 'var(--space-1)',
        'space-2': 'var(--space-2)',
        'space-3': 'var(--space-3)',
        'space-4': 'var(--space-4)',
        'space-5': 'var(--space-5)',
        'space-6': 'var(--space-6)',
        'space-7': 'var(--space-7)',
        'space-8': 'var(--space-8)',
      },
      // 使用设计令牌扩展字体大小
      fontSize: {
        'token-xs': ['var(--font-size-xs)', { lineHeight: 'var(--line-height-normal)' }],
        'token-sm': ['var(--font-size-sm)', { lineHeight: 'var(--line-height-normal)' }],
        'token-base': ['var(--font-size-base)', { lineHeight: 'var(--line-height-relaxed)' }],
        'token-lg': ['var(--font-size-lg)', { lineHeight: 'var(--line-height-relaxed)' }],
        'token-xl': ['var(--font-size-xl)', { lineHeight: 'var(--line-height-normal)' }],
        'token-2xl': ['var(--font-size-2xl)', { lineHeight: 'var(--line-height-normal)' }],
        'token-3xl': ['var(--font-size-3xl)', { lineHeight: 'var(--line-height-tight)' }],
        'token-4xl': ['var(--font-size-4xl)', { lineHeight: 'var(--line-height-tight)' }],
      },
      // 使用设计令牌扩展圆角
      borderRadius: {
        'token-none': 'var(--radius-none)',
        'token-sm': 'var(--radius-sm)',
        'token-base': 'var(--radius-base)',
        'token-md': 'var(--radius-md)',
        'token-lg': 'var(--radius-lg)',
        'token-xl': 'var(--radius-xl)',
        'token-full': 'var(--radius-full)',
      },
      // 使用设计令牌扩展阴影
      boxShadow: {
        'token-sm': 'var(--shadow-sm)',
        'token-base': 'var(--shadow-base)',
        'token-md': 'var(--shadow-md)',
        'token-lg': 'var(--shadow-lg)',
        'token-xl': 'var(--shadow-xl)',
      },
      // 使用设计令牌扩展动画时长
      transitionDuration: {
        'token-fast': 'var(--duration-fast)',
        'token-normal': 'var(--duration-normal)',
        'token-slow': 'var(--duration-slow)',
      },
      // 使用设计令牌扩展动画缓动函数
      transitionTimingFunction: {
        'token-in-out': 'var(--ease-in-out)',
        'token-out': 'var(--ease-out)',
        'token-in': 'var(--ease-in)',
      },
      // 使用设计令牌扩展z-index
      zIndex: {
        dropdown: 'var(--z-index-dropdown)',
        sticky: 'var(--z-index-sticky)',
        fixed: 'var(--z-index-fixed)',
        'modal-backdrop': 'var(--z-index-modal-backdrop)',
        modal: 'var(--z-index-modal)',
        popover: 'var(--z-index-popover)',
        tooltip: 'var(--z-index-tooltip)',
      },
      animation: {
        float: 'float 6s ease-in-out infinite',
        gradientFlow: 'gradientFlow 15s ease infinite',
        gradient: 'gradient 15s ease infinite',
        'gradient-slow': 'gradient 25s ease infinite',
      },
      backgroundImage: {
        'hero-gradient': 'linear-gradient(to bottom right, var(--tw-gradient-stops))',
        'button-gradient': 'linear-gradient(to right, var(--tw-gradient-stops))',
      },
    },
  },
  plugins: [typography],
}
