/// <reference types="node" />
/// <reference types="vite/client" />

import vue from '@vitejs/plugin-vue'
import { fileURLToPath } from 'node:url'
import { defineConfig, loadEnv } from 'vite'

// 字符串转换为 URL 友好的 slug 格式
function stringToSlug(str: string): string {
  if (!str) return ''
  return str
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')
    .replace(/[^\w-]+/g, '')
    .replace(/--+/g, '-')
    .replace(/^-+/, '')
    .replace(/-+$/, '')
}

/* global process, URL */

// CI 环境专用的精简配置
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())

  return {
    base: `/${stringToSlug(env.VITE_TEAM_NAME)}/`,
    plugins: [vue()], // 移除开发工具
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    // 极速构建配置
    build: {
      target: 'es2015', // 更宽松的目标
      modulePreload: false,
      sourcemap: false,
      reportCompressedSize: false, // 跳过 gzip 大小计算
      chunkSizeWarningLimit: 3000,
      cssCodeSplit: false, // 单一 CSS 文件
      minify: 'esbuild',
      cssMinify: 'esbuild', // 使用 esbuild 统一压缩
      rollupOptions: {
        output: {
          format: 'es',
          // 超级简化的分块 - 只分离最大的库
          manualChunks(id) {
            if (id.includes('node_modules')) {
              if (id.includes('ant-design-vue')) return 'ant'
              return 'vendor'
            }
          },
          entryFileNames: '[name].js',
          chunkFileNames: '[name].js',
          assetFileNames: '[name].[ext]',
        },
        treeshake: {
          preset: 'smallest',
          moduleSideEffects: false,
        },
      },
      // 极简压缩配置
      esbuild: {
        drop: ['console', 'debugger'],
        legalComments: 'none',
        minifyIdentifiers: true,
        minifySyntax: true,
        minifyWhitespace: true,
        treeShaking: true,
        pure: ['console.log', 'console.warn'],
        target: 'es2015',
      },
    },
    // 最小化依赖预构建
    optimizeDeps: {
      include: ['vue', 'vue-router', 'ant-design-vue'],
      force: false,
    },
  }
})
